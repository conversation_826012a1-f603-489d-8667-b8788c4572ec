# Project Overview

This is a Next.js web application called "Chess Duel Arena". It allows users to watch AI vs. AI chess matches, play against an AI, or watch "Freestyle" (Chess960) AI vs. AI matches. The application provides a rich user interface for configuring matches, viewing game history, and analyzing the AI's reasoning for each move.

## Key Technologies

- **Frontend:** Next.js, React, TypeScript
- **Styling:** Tailwind CSS, Radix UI (likely via a custom component library like shadcn/ui)
- **Backend/AI:** Genkit, Google AI (Gemini models)
- **Database:** PostgreSQL with Prisma ORM
- **Chess Logic:** `chess.js` library

## Architecture

The application follows a standard Next.js project structure.

- **`src/app`**: Contains the main application pages and API routes.
  - `page.tsx`: The main entry point, which renders the `ChessDuelArena` component.
  - `actions/history.ts`: Server-side actions for interacting with the game history in the database.
  - `api/autorun/route.ts`: An API route to trigger a background AI vs. AI game.
- **`src/components`**: Contains the React components.
  - `chess-duel-arena.tsx`: The core component that manages the entire game state, UI, and interactions.
  - `ui/`: A directory of UI components, likely from a library like shadcn/ui.
- **`src/ai`**: Contains the AI-related logic using Genkit.
  - `flows/generate-chess-move.ts`: Defines the Genkit flow and prompt for generating chess moves using a Gemini model.
- **`prisma`**: Contains the database schema definition (`schema.prisma`).

# Building and Running

## Prerequisites

- Node.js
- npm or yarn
- A PostgreSQL database

## Setup

1.  Install dependencies:
    ```bash
    npm install
    ```
2.  Set up your environment variables by creating a `.env` file and adding your `DATABASE_URL`. You can use `.env.example` as a template.
3.  Push the Prisma schema to your database:
    ```bash
    npx prisma db push
    ```

## Running the Application

- **Development Server:**

  ```bash
  npm run dev
  ```

  This will start the Next.js development server on `http://localhost:9002`.

- **Genkit AI Server (for AI development/debugging):**

  ```bash
  npm run genkit:dev
  ```

- **Production Build:**
  ```bash
  npm run build
  ```

## Deployment

### Railway Deployment

For deploying to Railway with optimized database connectivity, see the [Railway Deployment Guide](docs/RAILWAY_DEPLOYMENT.md).

Key features for Railway:

- Optimized PostgreSQL connection pooling
- Automatic retry logic for connection issues
- Health check endpoint at `/health`
- Graceful shutdown handling

* **Start Production Server:**
  ```bash
  npm run start
  ```

## Testing and Linting

- **Linting:**
  ```bash
  npm run lint
  ```
- **Type Checking:**
  ```bash
  npm run typecheck
  ```

# Development Conventions

- **Styling:** The project uses Tailwind CSS for utility-first styling. UI components are built using a component library based on Radix UI.
- **State Management:** Client-side state is managed using React hooks.
- **Data Fetching/Mutations:** Data is fetched and mutated using Next.js Server Actions (`src/app/actions/history.ts`).
- **AI:** AI logic is encapsulated in Genkit flows. The main flow (`generateMoveFlow`) uses a detailed prompt to guide the AI's reasoning process.
- **Database:** The database schema is managed with Prisma. All database interactions should go through the Prisma client.
