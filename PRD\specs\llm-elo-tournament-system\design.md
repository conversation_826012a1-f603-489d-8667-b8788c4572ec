# Design Document

## Overview

The LLM ELO Tournament System extends the existing Chess Duel Arena with a comprehensive rating system, tournament management, and automated scheduling capabilities. The system introduces three main components: LLM Profile Management with ELO ratings, Tournament Creation and Management, and Match History with detailed analytics.

The design leverages the existing Next.js architecture, Prisma database layer, and chess game engine while adding new database models, UI components, and background processing capabilities for automated tournament execution.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    UI[Sidebar Navigation UI] --> PM[Profile Manager]
    UI --> TM[Tournament Manager]
    UI --> HM[History Manager]

    PM --> ELO[ELO Calculator]
    TM --> SCHED[Tournament Scheduler]
    TM --> AUTO[Auto Match Executor]

    PM --> DB[(Database)]
    TM --> DB
    HM --> DB
    AUTO --> CHESS[Chess Engine]

    SCHED --> BG[Background Jobs]
    BG --> AUTO
```

### Database Architecture

The system extends the existing Prisma schema with four new models that integrate with the current `Game` model:

```mermaid
erDiagram
    LLMProfile {
        string id PK
        string name
        string model
        int eloRating
        int gamesPlayed
        int wins
        int losses
        int draws
        boolean isActive
        datetime createdAt
        datetime updatedAt
    }

    Tournament {
        string id PK
        string name
        string format
        string status
        datetime scheduledStart
        int matchInterval
        string timeWindow
        datetime createdAt
        datetime updatedAt
    }

    TournamentParticipant {
        string id PK
        string tournamentId FK
        string profileId FK
        int seed
        boolean eliminated
        datetime createdAt
    }

    Match {
        string id PK
        string tournamentId FK
        string whiteProfileId FK
        string blackProfileId FK
        string gameId FK
        string round
        string status
        int whiteEloChange
        int blackEloChange
        datetime scheduledAt
        datetime completedAt
        datetime createdAt
    }

    Game {
        string id PK
        string white
        string black
        string result
        string pgn
        string status
        json moveHistory
        json gameLogs
        datetime createdAt
        datetime updatedAt
    }

    Tournament ||--o{ TournamentParticipant : contains
    Tournament ||--o{ Match : generates
    LLMProfile ||--o{ TournamentParticipant : participates
    LLMProfile ||--o{ Match : "plays as white"
    LLMProfile ||--o{ Match : "plays as black"
    Match ||--|| Game : references
```

## Components and Interfaces

### 1. Sidebar Navigation Component

**Location**: `src/components/sidebar-navigation.tsx`

```typescript
interface SidebarNavigationProps {
  currentView: "profiles" | "tournaments" | "history" | "game";
  onViewChange: (view: string) => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}
```

Features:

- Collapsible sidebar with icon-only mode
- Navigation sections: Profiles, Tournaments, History, Live Game
- Active state indicators
- Responsive design for mobile

### 2. LLM Profile Management

**Location**: `src/components/profile-manager.tsx`

```typescript
interface LLMProfile {
  id: string;
  name: string;
  model: Model;
  eloRating: number;
  gamesPlayed: number;
  wins: number;
  losses: number;
  draws: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  recentMatches?: RecentMatch[];
}

interface RecentMatch {
  id: string;
  opponent: {
    name: string;
    model: string;
    eloRating: number;
  };
  result: "win" | "loss" | "draw";
  eloChange: number;
  playedAs: "white" | "black";
  completedAt: Date;
  tournament?: {
    name: string;
    round: string;
  };
}

interface ProfileManagerProps {
  profiles: LLMProfile[];
  onCreateProfile: (
    profile: Omit<LLMProfile, "id" | "createdAt" | "updatedAt">,
  ) => void;
  onUpdateProfile: (id: string, updates: Partial<LLMProfile>) => void;
  onDeleteProfile: (id: string) => void;
}
```

Features:

- Profile creation with model selection
- ELO rating display with volatility indicators (first 5 games, next 5 games, stable)
- Win/loss/draw statistics with percentages
- Recent match history (last 5-10 matches) showing:
  - Opponent name and model
  - Match result and ELO change (+/- with color coding)
  - Color played (white/black)
  - Tournament context if applicable
  - Date/time of match
- Profile activation/deactivation
- Detailed profile cards with expandable recent matches section
- Model display name with full model identifier tooltip

### 3. ELO Rating System

**Location**: `src/lib/elo-calculator.ts`

```typescript
interface ELOCalculationResult {
  whiteEloChange: number;
  blackEloChange: number;
  whiteNewElo: number;
  blackNewElo: number;
}

interface ELOCalculatorConfig {
  volatileGames: number; // 5
  stabilizingGames: number; // 10
  volatileKFactor: number; // 100
  stabilizingKFactor: number; // 50
  stableKFactor: number; // 20
}

class ELOCalculator {
  calculateEloChange(
    whiteElo: number,
    blackElo: number,
    whiteGames: number,
    blackGames: number,
    result: "white" | "black" | "draw",
  ): ELOCalculationResult;

  getKFactor(gamesPlayed: number): number;
  calculateExpectedScore(playerElo: number, opponentElo: number): number;
}
```

### 4. Tournament Management

**Location**: `src/components/tournament-manager.tsx`

```typescript
interface Tournament {
  id: string;
  name: string;
  format: "round-robin" | "single-elimination" | "double-elimination";
  status: "draft" | "scheduled" | "active" | "paused" | "completed";
  scheduledStart: Date;
  matchInterval: number; // minutes
  timeWindow: string; // "09:00-17:00"
  participants: TournamentParticipant[];
  matches: Match[];
  createdAt: Date;
  updatedAt: Date;
}

interface TournamentManagerProps {
  tournaments: Tournament[];
  profiles: LLMProfile[];
  onCreateTournament: (tournament: CreateTournamentInput) => void;
  onUpdateTournament: (id: string, updates: Partial<Tournament>) => void;
  onStartTournament: (id: string) => void;
  onPauseTournament: (id: string) => void;
}
```

### 5. Match History and Analytics

**Location**: `src/components/history-manager.tsx`

```typescript
interface MatchHistoryEntry {
  id: string;
  tournament?: Tournament;
  whiteProfile: LLMProfile;
  blackProfile: LLMProfile;
  game: Game;
  whiteEloChange: number;
  blackEloChange: number;
  completedAt: Date;
}

interface HistoryManagerProps {
  matches: MatchHistoryEntry[];
  onFilterChange: (filters: HistoryFilters) => void;
  onExportData: (format: "json" | "csv") => void;
}
```

## Data Models

### Extended Prisma Schema

```prisma
model LLMProfile {
  id          String   @id @default(cuid())
  name        String
  model       String   // Full model identifier (e.g., "gemini-2.0-flash", "openrouter/openai/gpt-oss-20b:free")
  eloRating   Int      @default(1900)
  gamesPlayed Int      @default(0)
  wins        Int      @default(0)
  losses      Int      @default(0)
  draws       Int      @default(0)
  isActive    Boolean  @default(true)
  lastMatchAt DateTime? // Track when last match was played
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  whiteMatches Match[] @relation("WhitePlayer")
  blackMatches Match[] @relation("BlackPlayer")
  tournaments  TournamentParticipant[]

  @@map("LLMProfile")
}

model Tournament {
  id            String            @id @default(cuid())
  name          String
  format        TournamentFormat
  status        TournamentStatus  @default(DRAFT)
  scheduledStart DateTime?
  matchInterval Int               @default(30) // minutes
  timeWindow    String            @default("00:00-23:59")
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  // Relations
  participants TournamentParticipant[]
  matches      Match[]

  @@map("Tournament")
}

model TournamentParticipant {
  id           String  @id @default(cuid())
  tournamentId String
  profileId    String
  seed         Int
  eliminated   Boolean @default(false)
  createdAt    DateTime @default(now())

  // Relations
  tournament Tournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)
  profile    LLMProfile  @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@unique([tournamentId, profileId])
  @@map("TournamentParticipant")
}

model Match {
  id              String    @id @default(cuid())
  tournamentId    String?
  whiteProfileId  String
  blackProfileId  String
  gameId          String?   @unique
  round           String?
  status          MatchStatus @default(SCHEDULED)
  whiteEloChange  Int?
  blackEloChange  Int?
  scheduledAt     DateTime?
  completedAt     DateTime?
  createdAt       DateTime  @default(now())

  // Relations
  tournament    Tournament? @relation(fields: [tournamentId], references: [id], onDelete: Cascade)
  whiteProfile  LLMProfile  @relation("WhitePlayer", fields: [whiteProfileId], references: [id])
  blackProfile  LLMProfile  @relation("BlackPlayer", fields: [blackProfileId], references: [id])
  game          Game?       @relation(fields: [gameId], references: [id])

  @@map("Match")
}

// Update existing Game model
model Game {
  id          String     @id @default(cuid())
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  white       String
  black       String
  result      String?
  pgn         String
  status      GameStatus @default(IN_PROGRESS)
  moveHistory Json?
  gameLogs    Json?

  // New relation
  match       Match?

  @@map("Game")
}

enum TournamentFormat {
  ROUND_ROBIN
  SINGLE_ELIMINATION
  DOUBLE_ELIMINATION
}

enum TournamentStatus {
  DRAFT
  SCHEDULED
  ACTIVE
  PAUSED
  COMPLETED
}

enum MatchStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  FAILED
}
```

## Error Handling

### ELO Calculation Errors

- Invalid game results: Default to draw (0.5 points each)
- Negative ELO ratings: Minimum rating floor of 100
- Calculation overflow: Cap maximum single-game change at ±200 points

### Tournament Management Errors

- Insufficient participants: Minimum 2 players required
- Scheduling conflicts: Queue matches when models are busy
- Failed matches: Automatic retry with exponential backoff

### Database Errors

- Transaction rollbacks for ELO updates
- Cascade deletion protection for active tournaments
- Data consistency checks for match results

## Testing Strategy

### Unit Tests

- ELO calculation accuracy with various scenarios
- Tournament bracket generation algorithms
- Match scheduling logic
- Profile management operations

### Integration Tests

- End-to-end tournament execution
- Database transaction integrity
- API endpoint functionality
- Background job processing

### Performance Tests

- Large tournament handling (100+ participants)
- Concurrent match execution
- Database query optimization
- Memory usage during long tournaments

### User Acceptance Tests

- Tournament creation workflow
- Profile management interface
- Match history filtering and export
- Automated scheduling accuracy

## Security Considerations

### Data Protection

- Profile data validation and sanitization
- Tournament access control
- Match result integrity verification
- Export data filtering

### API Security

- Rate limiting for tournament operations
- Input validation for all endpoints
- Authentication for administrative functions
- Audit logging for ELO changes

### Background Processing

- Job queue security and isolation
- Resource usage monitoring
- Graceful failure handling
- Process restart capabilities
