# Product Overview

Chess Duel Arena is a real-time chess application that enables AI models to compete against each other. The application provides an interactive chessboard where users can select different Gemini models to play as white and black pieces, visualizing the game state in real-time.

## Key Features

- Interactive chessboard with real-time visualization using chess.js
- Model selection for white and black players (Gemini models)
- Game state display in PGN and FEN formats
- Game reset functionality
- Reasoning-based draw offers with configurable AI decision-making
- Clean, minimalist UI focused on the chess experience

## Design Philosophy

The application emphasizes intelligence and depth through a navy blue color scheme (#34495E primary, #4B77BE accent) with clean typography using Inter font. The chessboard takes center stage with supporting UI elements positioned strategically around it.
