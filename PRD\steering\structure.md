# Project Structure

## Root Directory

```
├── .env / .env.example     # Environment variables
├── package.json            # Dependencies and scripts
├── next.config.ts          # Next.js configuration
├── tailwind.config.ts      # Tailwind CSS configuration
├── tsconfig.json          # TypeScript configuration
├── components.json        # Shadcn/ui configuration
├── prisma/               # Database schema and migrations
└── docs/                 # Project documentation
```

## Source Directory (`src/`)

### Application Structure (`src/app/`)

- **App Router** - Next.js 13+ file-based routing
- `layout.tsx` - Root layout component
- `page.tsx` - Home page component
- `globals.css` - Global styles and CSS variables
- `actions/` - Server actions
- `api/` - API route handlers
- `health/` - Health check endpoints

### AI Integration (`src/ai/`)

- `genkit.ts` - Genkit configuration and setup
- `dev.ts` - Development server entry point
- `flows/` - AI workflow definitions

### Components (`src/components/`)

- `ui/` - Shadcn/ui components (<PERSON>ton, <PERSON><PERSON>, etc.)
- `chess-duel-arena.tsx` - Main chess game component
- Follow component composition patterns

### Utilities (`src/lib/`)

- `utils.ts` - Shared utility functions (cn, etc.)
- `db.ts` - Database connection and Prisma client

### Custom Hooks (`src/hooks/`)

- `use-mobile.tsx` - Mobile detection hook
- `use-toast.ts` - Toast notification hook

## Database (`prisma/`)

- `schema.prisma` - Database schema with Game model
- Supports PostgreSQL with game state tracking
- Uses CUID for primary keys

## Styling Conventions

- **Tailwind CSS** with CSS variables for theming
- **HSL color system** for consistent theming
- **Component variants** using class-variance-authority
- **Responsive design** with mobile-first approach

## Import Patterns

- Use `@/` alias for src imports: `@/components/ui/button`
- Absolute imports preferred over relative
- Group imports: external libraries, internal modules, relative imports

## File Naming

- **kebab-case** for component files: `chess-duel-arena.tsx`
- **camelCase** for utility files: `useToast.ts`
- **PascalCase** for component names in code
