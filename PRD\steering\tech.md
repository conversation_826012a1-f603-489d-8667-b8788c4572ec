# Technology Stack

## Framework & Runtime

- **Next.js 15.3.3** - React framework with App Router
- **React 18** - UI library
- **TypeScript 5.9.2** - Type-safe JavaScript
- **Node.js** - Runtime environment

## AI & Backend

- **Firebase Genkit** - AI application framework
- **Gemini AI** - Google's AI models for chess gameplay
- **Prisma** - Database ORM with PostgreSQL
- **Express** - Server framework for API routes

## UI & Styling

- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Headless component library
- **Shadcn/ui** - Pre-built component system
- **Lucide React** - Icon library
- **Inter font** - Primary typography

## Chess Engine

- **chess.js** - Chess game logic and validation
- **react-chessboard** - Interactive chessboard component

## Development Tools

- **ESLint** - Code linting (Next.js config)
- **Turbopack** - Fast bundler for development
- **Patch Package** - NPM package patching

## Common Commands

### Development

```bash
npm run dev          # Start development server on port 9002 with Turbopack
npm run genkit:dev   # Start Genkit development server
npm run genkit:watch # Start Genkit with file watching
```

### Build & Deploy

```bash
npm run build        # Generate Prisma client and build for production
npm start           # Deploy migrations and start production server
```

### Code Quality

```bash
npm run lint         # Run ESLint
npm run typecheck    # Run TypeScript compiler checks
```

## Configuration Notes

- TypeScript and ESLint errors are ignored during builds (configured in next.config.ts)
- Path aliases use `@/*` for `./src/*`
- Turbopack enabled for faster development builds
- CSS variables used for theming with HSL color system
