import {NextRequest, NextResponse} from 'next/server';
import {Chess} from 'chess.js';
import type {GenerateMoveOutput} from '@/ai/flows/generate-chess-move';
import {generateMove} from '@/ai/flows/generate-chess-move';
import {saveGame} from '@/app/actions/history';

// This ensures the route is always treated as dynamic
export const dynamic = 'force-dynamic';

type Player = 'white' | 'black';

async function runGame() {
  const game = new Chess();
  const whiteModel = process.env.AUTORUN_WHITE_MODEL || 'gemini-2.0-flash';
  const blackModel = process.env.AUTORUN_BLACK_MODEL || 'gemini-2.5-pro';

  game.header('Event', `Autorun: ${whiteModel} vs ${blackModel}`);
  game.header('Site', 'Railway Backend');
  game.header('Date', new Date().toISOString().split('T')[0]);
  game.header('Round', '1');
  game.header('White', whiteModel);
  game.header('Black', blackModel);

  while (!game.isGameOver()) {
    const currentPlayer: Player = game.turn() === 'w' ? 'white' : 'black';
    const currentModel = currentPlayer === 'white' ? whiteModel : blackModel;
    let moveSuccessful = false;
    let attempts = 0;

    while (attempts < 5 && !moveSuccessful) {
      attempts++;
      try {
        const legalMoves = game.moves({verbose: false});
        const result: GenerateMoveOutput = await generateMove({
          fen: game.fen(),
          pgn: game.pgn(),
          player: currentPlayer,
          reasoningMode: false,
          model: currentModel,
          legalMoves: legalMoves,
        });

        if (result && result.move && game.move(result.move)) {
          moveSuccessful = true;
        } else {
          console.error(`AUTORUN: Invalid move ${result.move} from ${currentModel}. Attempt ${attempts}`);
        }
      } catch (error) {
        console.error(`AUTORUN: Error generating move for ${currentModel}. Attempt ${attempts}`, error);
      }
    }

    if (!moveSuccessful) {
      console.error(`AUTORUN: ${currentModel} failed to make a valid move after 5 attempts. Aborting game.`);
      // End the game by resignation
      const result = currentPlayer === 'white' ? '0-1' : '1-0';
      game.header('Result', result);
      break; 
    }
  }

  // Determine result
  let result = '*';
  if (game.isCheckmate()) {
    result = game.turn() === 'b' ? '1-0' : '0-1';
  } else if (game.isDraw() || game.isStalemate() || game.isThreefoldRepetition() || game.isInsufficientMaterial()) {
    result = '1/2-1/2';
  } else if (game.header().Result) {
      result = game.header().Result;
  }
  
  game.header('Result', result);

  await saveGame({
    white: game.header().White || 'Unknown',
    black: game.header().Black || 'Unknown',
    result: result as '1-0' | '0-1' | '1/2-1/2',
    pgn: game.pgn(),
  });

  console.log(`AUTORUN: Game finished. PGN: ${game.pgn()}`);
  return game.pgn();
}

export async function GET(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  const cronSecret = process.env.CRON_SECRET;

  if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
    return new NextResponse('Unauthorized', {status: 401});
  }

  try {
    const pgn = await runGame();
    return NextResponse.json({
      message: 'Autorun game finished successfully.',
      pgn: pgn,
    });
  } catch (error) {
    console.error('AUTORUN: A fatal error occurred during game execution.', error);
    return new NextResponse('Internal Server Error', {status: 500});
  }
}