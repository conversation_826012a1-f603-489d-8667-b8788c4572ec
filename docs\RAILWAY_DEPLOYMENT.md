# Railway Deployment Guide

This guide covers deploying the Chess AI application to Railway with optimized database connectivity.

## Quick Setup

1. **Connect your repository to Railway**
   - Go to [Railway](https://railway.app)
   - Create a new project from your GitHub repository

2. **Add a PostgreSQL database**
   - In your Railway project, click "New Service"
   - Select "Database" → "PostgreSQL"
   - Railway will automatically provide `DATABASE_URL` and `DIRECT_URL`

3. **Configure environment variables**

   ```bash
   # Required
   GOOGLE_AI_API_KEY=your_google_ai_api_key
   OPENROUTER_API_KEY=your_openrouter_api_key

   # Optional optimizations
   DATABASE_CONNECTION_LIMIT=5
   DATABASE_POOL_TIMEOUT=10
   NODE_ENV=production
   ```

## Database Connection Optimization

The application includes several optimizations for Railway's PostgreSQL:

### Connection Pooling

- Limited to 5 connections by default (configurable via `DATABASE_CONNECTION_LIMIT`)
- 10-second pool timeout (configurable via `DATABASE_POOL_TIMEOUT`)
- Automatic connection health checks every 30 seconds

### Error Handling

- Automatic retry logic for connection issues
- Graceful shutdown handling for Railway's SIGTERM signals
- Circuit breaker pattern for database operations

### Health Checks

- `/health` endpoint validates database connectivity
- Configured in `railway.json` with 300-second timeout
- Supports both GET and HEAD requests

## Troubleshooting Connection Issues

### "Connection reset by peer" errors

This is typically caused by:

1. **Too many connections** - Reduce `DATABASE_CONNECTION_LIMIT`
2. **Long-running queries** - Check for slow queries in your application
3. **Network timeouts** - The app now includes automatic retry logic

### Deployment fails

1. Check Railway logs for specific error messages
2. Verify all required environment variables are set
3. Ensure database migrations run successfully

### Application won't start

1. Check the health endpoint: `https://your-app.railway.app/health`
2. Review Railway deployment logs
3. Verify database connectivity from Railway dashboard

## Monitoring

### Health Check Endpoint

```bash
curl https://your-app.railway.app/health
```

Response format:

```json
{
  "status": "healthy",
  "timestamp": "2025-08-08T10:30:00.000Z",
  "checks": {
    "database": "ok"
  },
  "responseTime": 45,
  "environment": {
    "nodeEnv": "production",
    "railwayEnvironment": "production",
    "deploymentId": "abc123"
  }
}
```

### Database Metrics

Monitor these in Railway dashboard:

- Connection count
- Query response times
- Error rates
- Memory usage

## Best Practices

1. **Environment Variables**
   - Use Railway's built-in `DATABASE_URL` and `DIRECT_URL`
   - Set `NODE_ENV=production`
   - Configure connection limits based on your plan

2. **Database Management**
   - Use `prisma migrate deploy` for production migrations
   - Monitor connection pool usage
   - Set up database backups in Railway

3. **Error Monitoring**
   - Check Railway logs regularly
   - Set up log aggregation if needed
   - Monitor the `/health` endpoint

## Configuration Files

The following files are optimized for Railway:

- `railway.json` - Deployment configuration
- `scripts/railway-start.js` - Startup script with health checks
- `src/lib/db.ts` - Database connection management
- `src/app/health/route.ts` - Health check endpoint

## Support

If you encounter issues:

1. Check Railway's status page
2. Review the application logs in Railway dashboard
3. Test database connectivity using the health endpoint
4. Verify environment variables are correctly set
