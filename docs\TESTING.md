# Testing Guide

This document outlines the comprehensive testing strategy for the Chess Duel Arena application.

## 🧪 Testing Stack

- **Unit Testing:** Vitest + React Testing Library
- **E2E Testing:** Playwright
- **Coverage:** V8 Coverage Provider
- **CI/CD:** GitHub Actions

## 📁 Test Structure

```
src/test/
├── setup.ts                 # Test environment setup
├── utils.tsx                # Test utilities and mocks
├── lib/                     # Unit tests for libraries
│   ├── profile-client.test.ts
│   └── tournament-client.test.ts
├── components/              # Component tests
│   └── chess-duel-arena.test.tsx
├── api/                     # API route tests
│   ├── profiles.test.ts
│   └── spectate.test.ts
└── e2e/                     # End-to-end tests
    └── chess-game.spec.ts
```

## 🚀 Running Tests

### Unit Tests

```bash
# Run all unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

### E2E Tests

```bash
# Run all E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run E2E tests in headed mode
npm run test:e2e:headed
```

### All Tests

```bash
# Run all tests (unit + E2E)
npm run test:all

# Run CI tests (with coverage)
npm run test:ci
```

## 🎯 Test Coverage Goals

- **Unit Tests:** >80% coverage
- **Integration Tests:** Critical user flows
- **E2E Tests:** Main application features
- **API Tests:** All endpoints

## 📋 Test Categories

### 1. Unit Tests

#### Client Services

- ✅ Profile client service (12 tests)
- ✅ Tournament client service (14 tests)
- ✅ Error handling and network failures
- ✅ API response validation

#### Components

- ✅ Chess board rendering
- ✅ Game controls functionality
- ✅ Move history display
- ✅ AI reasoning panel
- ✅ Navigation buttons
- ✅ Copy functionality

#### API Routes

- ✅ Profile CRUD operations
- ✅ Tournament management
- ✅ Game spectating
- ✅ Error handling
- ✅ Input validation

### 2. Integration Tests

#### WritableStream Handling

- ✅ Stream closure detection
- ✅ Error recovery
- ✅ Client disconnection handling

#### Database Operations

- ✅ Profile management
- ✅ Tournament lifecycle
- ✅ Game state persistence

### 3. E2E Tests

#### Core Functionality

- ✅ Chess game interface loading
- ✅ Game mode selection
- ✅ Move history and AI reasoning
- ✅ Navigation controls
- ✅ Copy FEN/PGN functionality
- ✅ Game start/reset flow
- ✅ Autorun functionality
- ✅ Spectate mode
- ✅ Mobile responsiveness

#### Error Scenarios

- ✅ Network error handling
- ✅ Invalid game states
- ✅ Browser compatibility

## 🔧 Test Configuration

### Vitest Config

```typescript
// vitest.config.ts
export default defineConfig({
  plugins: [react()],
  test: {
    environment: "jsdom",
    setupFiles: ["./src/test/setup.ts"],
    globals: true,
    css: true,
    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html"],
      exclude: [
        "node_modules/",
        "src/test/",
        "**/*.d.ts",
        "**/*.config.*",
        "coverage/",
        ".next/",
        "prisma/",
      ],
    },
  },
});
```

### Playwright Config

```typescript
// playwright.config.ts
export default defineConfig({
  testDir: "./src/test/e2e",
  fullyParallel: true,
  retries: process.env.CI ? 2 : 0,
  use: {
    baseURL: "http://localhost:3000",
    trace: "on-first-retry",
    screenshot: "only-on-failure",
  },
  projects: [
    { name: "chromium", use: { ...devices["Desktop Chrome"] } },
    { name: "firefox", use: { ...devices["Desktop Firefox"] } },
    { name: "webkit", use: { ...devices["Desktop Safari"] } },
    { name: "Mobile Chrome", use: { ...devices["Pixel 5"] } },
    { name: "Mobile Safari", use: { ...devices["iPhone 12"] } },
  ],
});
```

## 🛡️ Error Prevention

### Runtime Error Prevention

1. **WritableStream Checks:** Prevent closed stream writes
2. **Database Connection:** Proper error handling
3. **API Validation:** Input sanitization
4. **Type Safety:** TypeScript strict mode
5. **Mock Validation:** Comprehensive mocking

### Test-Driven Development

1. Write tests before implementation
2. Red-Green-Refactor cycle
3. Test edge cases and error conditions
4. Maintain high test coverage

## 📊 Current Test Status

### ✅ Passing Tests (165/190 - 87%)

### 🔄 Continuous Improvement

- Fix failing tests
- Increase coverage to >90%
- Add performance tests
- Add accessibility tests

## 🚀 CI/CD Integration

### GitHub Actions Pipeline

1. **Lint & Type Check**
2. **Unit Tests with Coverage**
3. **E2E Tests**
4. **Security Audit**
5. **Build Verification**
6. **Deployment (on main branch)**

### Quality Gates

- All tests must pass
- Coverage >80%
- No security vulnerabilities
- Successful build

## 📝 Writing New Tests

### Unit Test Template

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '../utils'

describe('ComponentName', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render correctly', () => {
    render(<ComponentName />)
    expect(screen.getByText('Expected Text')).toBeInTheDocument()
  })
})
```

### E2E Test Template

```typescript
import { test, expect } from "@playwright/test";

test.describe("Feature Name", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/");
  });

  test("should perform action", async ({ page }) => {
    await page.getByText("Button").click();
    await expect(page.getByText("Result")).toBeVisible();
  });
});
```

## 🎯 Best Practices

1. **Test Isolation:** Each test should be independent
2. **Clear Naming:** Descriptive test names
3. **Arrange-Act-Assert:** Clear test structure
4. **Mock External Dependencies:** Use proper mocking
5. **Test Edge Cases:** Cover error scenarios
6. **Performance:** Keep tests fast and reliable
7. **Maintenance:** Update tests with code changes
