{"name": "nextn", "version": "0.1.0", "private": true, "engines": {"node": ">=20.19.0"}, "scripts": {"dev": "next dev --turbopack -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "prisma generate && next build", "start": "node scripts/railway-start.js", "start:simple": "prisma migrate deploy && next start", "lint": "next lint", "typecheck": "tsc --noEmit", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:all": "npm run test && npm run test:e2e", "test:ci": "npm run test:coverage && npm run test:e2e"}, "dependencies": {"@genkit-ai/compat-oai": "^1.15.5", "@genkit-ai/googleai": "^1.14.1", "@genkit-ai/next": "^1.14.1", "@hookform/resolvers": "^4.1.3", "@prisma/client": "^6.13.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "chess.js": "^1.0.0-beta.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "firebase": "^11.9.1", "genkit": "^1.14.1", "import-in-the-middle": "^1.14.2", "lucide-react": "^0.475.0", "next": "15.3.3", "patch-package": "^8.0.0", "react": "^18.3.1", "react-chessboard": "^4.3.0", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@playwright/test": "^1.54.2", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@vitejs/plugin-react": "^5.0.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "eslint": "^9.32.0", "eslint-config-next": "^15.4.6", "genkit-cli": "^1.14.1", "jest": "^30.0.5", "jsdom": "^26.1.0", "postcss": "^8", "prisma": "^6.13.0", "tailwindcss": "^3.4.1", "ts-jest": "^29.4.1", "typescript": "5.9.2", "vitest": "^3.2.4"}}