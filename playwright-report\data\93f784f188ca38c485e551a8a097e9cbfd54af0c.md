# Page snapshot

```yaml
- main:
  - button "Toggle Sidebar":
    - img
    - text: Toggle Sidebar
  - heading "Live Game" [level=1]:
    - img
    - text: Live Game
  - button "Toggle theme":
    - img
  - heading "Chess Duel Arena" [level=1]
  - paragraph: AI vs AI Chess Simulation
  - text: Play Chess Select a game mode
  - img
  - text: Ready
  - img
  - paragraph: Play vs AI
  - img
  - paragraph: AI vs AI
  - img
  - paragraph: Freestyle AI vs AI
  - img:
    - img
  - text: "8"
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - text: "7"
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - text: 6 5 4 3
  - img:
    - img
  - text: "2"
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - text: 1 a
  - img:
    - img
  - text: b
  - img:
    - img
  - text: c
  - img:
    - img
  - text: d
  - img:
    - img
  - text: e
  - img:
    - img
  - text: f
  - img:
    - img
  - text: g
  - img:
    - img
  - text: h
  - img
  - text: Game has not started.
  - button "Prev" [disabled]:
    - img
    - text: Prev
  - text: "Move: 0 / 0"
  - button "Next" [disabled]:
    - img
    - text: Next
  - text: Game Info Welcome to Chess Duel Arena!
  - img
  - text: Game History Previously completed games
  - img
  - text: Loading game history...
- region "Notifications (F8)":
  - list
- alert
- button "Open Next.js Dev Tools":
  - img
```