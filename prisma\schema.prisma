generator client {
  provider = "prisma-client-js"
  previewFeatures = ["tracing"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model LLMProfile {
  id          String   @id @default(cuid())
  name        String
  model       String   // Full model identifier (e.g., "gemini-2.0-flash", "openrouter/openai/gpt-oss-20b:free")
  eloRating   Int      @default(1900)
  gamesPlayed Int      @default(0)
  wins        Int      @default(0)
  losses      Int      @default(0)
  draws       Int      @default(0)
  isActive    Boolean  @default(true)
  lastMatchAt DateTime? // Track when last match was played
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  whiteMatches Match[] @relation("WhitePlayer")
  blackMatches Match[] @relation("BlackPlayer")
  tournaments  TournamentParticipant[]

  @@map("LLMProfile")
}

model Tournament {
  id            String            @id @default(cuid())
  name          String
  format        TournamentFormat
  status        TournamentStatus  @default(DRAFT)
  scheduledStart DateTime?
  matchInterval Int               @default(30) // minutes
  timeWindow    String            @default("00:00-23:59")
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  // Relations
  participants TournamentParticipant[]
  matches      Match[]

  @@map("Tournament")
}

model TournamentParticipant {
  id           String  @id @default(cuid())
  tournamentId String
  profileId    String
  seed         Int
  eliminated   Boolean @default(false)
  createdAt    DateTime @default(now())

  // Relations
  tournament Tournament @relation(fields: [tournamentId], references: [id], onDelete: Cascade)
  profile    LLMProfile  @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@unique([tournamentId, profileId])
  @@map("TournamentParticipant")
}

model Match {
  id              String    @id @default(cuid())
  tournamentId    String?
  whiteProfileId  String
  blackProfileId  String
  gameId          String?   @unique
  round           String?
  status          MatchStatus @default(SCHEDULED)
  whiteEloChange  Int?
  blackEloChange  Int?
  scheduledAt     DateTime?
  completedAt     DateTime?
  createdAt       DateTime  @default(now())

  // Relations
  tournament    Tournament? @relation(fields: [tournamentId], references: [id], onDelete: Cascade)
  whiteProfile  LLMProfile  @relation("WhitePlayer", fields: [whiteProfileId], references: [id])
  blackProfile  LLMProfile  @relation("BlackPlayer", fields: [blackProfileId], references: [id])
  game          Game?       @relation(fields: [gameId], references: [id])

  @@map("Match")
}

model Game {
  id              String     @id @default(cuid())
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt
  white           String
  black           String
  result          String?
  pgn             String
  status          GameStatus @default(IN_PROGRESS)
  moveHistory     Json?
  gameLogs        Json?
  reasoningHistory Json?     // Store AI reasoning for each move
  lastMoveAt      DateTime?

  // New relation
  match           Match?

  @@map("Game")
}

model GameActivity {
  id              String   @id @default(cuid())
  type            String   // 'game_start', 'game_end', 'elo_update', 'move', 'reasoning'
  whitePlayer     String
  blackPlayer     String
  whiteElo        Int?
  blackElo        Int?
  whiteEloChange  Int?
  blackEloChange  Int?
  result          String?  // '1-0', '0-1', '1/2-1/2'
  move            String?
  reasoning       String?  @db.Text
  gameId          String?
  metadata        Json? // JSON string for additional data
  createdAt       DateTime @default(now())

  @@index([gameId])
  @@index([type])
  @@index([createdAt])
  @@map("GameActivity")
}

enum TournamentFormat {
  ROUND_ROBIN
  SINGLE_ELIMINATION
  DOUBLE_ELIMINATION
}

enum TournamentStatus {
  DRAFT
  SCHEDULED
  ACTIVE
  PAUSED
  COMPLETED
}

enum MatchStatus {
  SCHEDULED
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum GameStatus {
  IN_PROGRESS
  COMPLETED
  FAILED
}
