"use server";

/**
 * @fileOverview A Genkit flow for generating a chess move based on the game state.
 *
 * - generateMove - A function that determines the AI's next move.
 * - GenerateMoveInput - The input type for the generateMove function.
 * - GenerateMoveOutput - The return type for the generateMove function.
 */

import { ai } from "@/ai/genkit";
import { googleAI } from "@genkit-ai/googleai";
import { openAICompatible } from "@genkit-ai/compat-oai";
import { z } from "zod";

const GenerateMoveInputSchema = z.object({
  fen: z
    .string()
    .describe("The current board state in Forsyth-Edwards Notation (FEN)."),
  pgn: z
    .string()
    .describe(
      "The history of moves in the game in Portable Game Notation (PGN).",
    ),
  player: z
    .enum(["white", "black"])
    .describe("The current player to make a move."),
  reasoningMode: z.boolean().describe("Whether to enable detailed reasoning."),
  model: z
    .string()
    .describe("The name of the model to use for generating the move."),
  legalMoves: z
    .array(z.string())
    .describe(
      "A list of all legal moves in Standard Algebraic Notation (SAN) for the current position.",
    ),
  isChess960: z
    .boolean()
    .optional()
    .describe("Set to true if the game is a Chess960 variant."),
});
export type GenerateMoveInput = z.infer<typeof GenerateMoveInputSchema>;

const GenerateMoveOutputSchema = z.object({
  move: z
    .string()
    .describe(
      "The best move in Standard Algebraic Notation (e.g., Nf3, e4, O-O).",
    ),
  reason: z
    .string()
    .describe(
      "A detailed, multi-sentence reasoning for the chosen move, explaining the strategic thinking behind it.",
    ),
  opponentPrediction: z
    .string()
    .describe(
      "The opponent's most likely response to the chosen move, in Standard Algebraic Notation (e.g., Nf6).",
    )
    .optional(),
  analysis: z
    .array(
      z.object({
        move: z
          .string()
          .describe(
            "A possible move in Standard Algebraic Notation (e.g., Nf3, d4).",
          ),
        consideration: z
          .string()
          .describe(
            "Why this move was considered a good idea. What was its strategic merit?",
          ),
        rejectionReason: z
          .string()
          .describe(
            "Why this move was ultimately not chosen. What was its flaw or the drawback compared to the chosen move?",
          ),
      }),
    )
    .optional()
    .describe(
      "An analysis of a few strong candidate moves that were considered but ultimately rejected.",
    ),
});
export type GenerateMoveOutput = z.infer<typeof GenerateMoveOutputSchema>;

export async function generateMove(
  input: GenerateMoveInput,
): Promise<GenerateMoveOutput> {
  return generateMoveFlow(input);
}

const generateMovePrompt = ai.definePrompt({
  name: "generateMovePrompt",
  input: { schema: GenerateMoveInputSchema },
  output: { schema: GenerateMoveOutputSchema },
  prompt: `You are a world-class chess grandmaster AI with encyclopedic knowledge of chess theory. Your strategic thinking is deep, and your explanations are detailed and insightful.
    You are playing as the {{player}} pieces.
    The current board state (FEN) is: {{{fen}}}
    The game history (PGN) is: {{{pgn}}}

    {{#if isChess960}}
    **IMPORTANT NOTE:** This is a Chess960 (Freestyle) game. The starting position was randomized. DO NOT refer to standard chess opening names (e.g., "Queen's Gambit", "Sicilian Defense"). Your analysis must be based purely on the principles of chess strategy and the specific, unique position on the board.
    {{else}}
    **Opening Knowledge:** If this is the opening phase (first ~15 moves), identify the opening variation and reference classical games by masters when relevant. Explain the opening principles being followed or violated.
    {{/if}}

    Your task is to perform a comprehensive analysis and select the optimal move. Follow this thought process:

    **1. Deep Positional Evaluation:**
    - **Material Advantage:** First, count the material for both sides (Pawn=1, Knight=3, Bishop=3, Rook=5, Queen=9). State this clearly in your reasoning.
    - **King Safety:** Analyze castling status, king exposure, and potential mating threats.
    - **Center Control:** Evaluate control of central squares (e4, e5, d4, d5).
    - **Piece Activity:** Assess piece coordination, outposts, and weak pieces.
    - **Pawn Structure:** Identify pawn weaknesses, chains, and passed pawns.
    - **Strategic Themes:** Identify key strategic elements (weak squares, color complexes, files, diagonals).
    - **Immediate Threats:** All tactical threats for both sides.

    **2. Candidate Moves Analysis:**
    - Identify 5-7 strongest candidate moves that improve your position, create threats, or respond to opponent plans.
    - Consider moves that: develop pieces, improve king safety, create threats, defend against opponent threats, improve pawn structure, gain space.

    **3. Final Move Selection & Detailed Reasoning:**
    - Select the single best move from your candidates.
    - Provide comprehensive reasoning (4-6 sentences minimum) in the 'reason' field that includes:
      • Material count
      • Opening theory (if applicable)
      • Strategic justification
      • Tactical considerations
      • Long-term plans
      • Why this move is superior to alternatives

    **4. Legal Moves List:**
    You MUST choose from these legal moves in Standard Algebraic Notation:
    {{#each legalMoves}}
    - {{this}}
    {{/each}}

    {{#if reasoningMode}}
    **5. Detailed Analysis (REQUIRED):**
    - **'opponentPrediction' field:** Predict opponent's most likely response with brief reasoning.
    - **'analysis' field:** For exactly 5-7 strong candidate moves you rejected, provide detailed analysis:
       • 'move': The move notation
       • 'consideration': Strategic merit, tactical ideas, positional benefits (2-3 sentences)
       • 'rejectionReason': Specific flaws, why inferior to chosen move (2-3 sentences)

    Make your reasoning thorough and educational - think like a chess instructor explaining to an advanced student.
    {{/if}}

    Your response MUST be valid JSON with the move from the legal moves list.`,
});

const generateMoveFlow = ai.defineFlow(
  {
    name: "generateMoveFlow",
    inputSchema: GenerateMoveInputSchema,
    outputSchema: GenerateMoveOutputSchema,
  },
  async (input) => {
    try {
      // Determine which provider to use based on model name
      let modelProvider;
      if (input.model.startsWith("openrouter/")) {
        // Use OpenRouter models
        const modelName = input.model.replace("openrouter/", "");
        modelProvider = `openrouter/${modelName}`;
      } else {
        // Use Google AI for gemini models
        modelProvider = googleAI.model(input.model);
      }

      const { output } = await generateMovePrompt(input, {
        model: modelProvider,
        config: {
          temperature: 0.0,
        },
      });
      return output!;
    } catch (error: any) {
      // If the primary model fails due to rate limiting, try a fallback model
      if (error.status === 429 && !input.model.startsWith("openrouter/")) {
        console.warn(
          `Model ${input.model} is rate-limited. Attempting fallback to OpenRouter.`,
        );
        try {
          const fallbackModel =
            "openrouter/nousresearch/nous-hermes-2-mixtral-8x7b-dpo";
          const { output } = await generateMovePrompt(input, {
            model: fallbackModel,
            config: {
              temperature: 0.0,
            },
          });
          // Add a note to the reason that a fallback model was used
          output!.reason = `(Fallback to Hermes) ${output!.reason}`;
          return output!;
        } catch (fallbackError) {
          console.error("Fallback model also failed.", fallbackError);
          // If fallback also fails, then return a random move
        }
      }

      // For non-rate-limit errors or if fallback fails
      console.error(
        "Error in generateMoveFlow, returning a random move.",
        error,
      );
      const randomMove =
        input.legalMoves[Math.floor(Math.random() * input.legalMoves.length)];
      return {
        move: randomMove,
        reason:
          "The AI failed to generate a valid response, so a random move was chosen.",
      };
    }
  },
);
