"use server";

import prisma from "@/lib/db";
import { z } from "zod";
import { revalidatePath } from "next/cache";

const SaveGameSchema = z.object({
  white: z.string(),
  black: z.string(),
  result: z.enum(["1-0", "0-1", "1/2-1/2", "*"]),
  pgn: z.string(),
  gameId: z.string().optional(),
  status: z.enum(["IN_PROGRESS", "COMPLETED", "FAILED"]).optional(),
  reasoningHistory: z.array(z.any()).optional(),
});

type SaveGameInput = z.infer<typeof SaveGameSchema>;

export async function saveGame(data: SaveGameInput) {
  try {
    const validatedData = SaveGameSchema.parse(data);

    // Determine status based on result if not provided
    const status =
      validatedData.status ||
      (validatedData.result === "*" ? "IN_PROGRESS" : "COMPLETED");

    // If gameId is provided, try to update existing game first
    if (validatedData.gameId) {
      try {
        const existingGame = await prisma.game.findUnique({
          where: { id: validatedData.gameId },
        });

        if (existingGame) {
          const updatedGame = await prisma.game.update({
            where: { id: validatedData.gameId },
            data: {
              white: validatedData.white,
              black: validatedData.black,
              result: validatedData.result,
              pgn: validatedData.pgn,
              status,
              reasoningHistory: validatedData.reasoningHistory,
            },
          });
          revalidatePath("/");
          return updatedGame;
        }
      } catch (updateError) {
        console.warn(
          "Failed to update existing game, creating new one:",
          updateError,
        );
      }
    }

    // Create new game
    const savedGame = await prisma.game.create({
      data: {
        id: validatedData.gameId,
        white: validatedData.white,
        black: validatedData.black,
        result: validatedData.result,
        pgn: validatedData.pgn,
        status,
        reasoningHistory: validatedData.reasoningHistory,
      },
    });
    revalidatePath("/");
    return savedGame;
  } catch (error) {
    console.error("Failed to save game:", error);
    throw new Error("Could not save the game to the database.");
  }
}

export async function getGames() {
  try {
    const games = await prisma.game.findMany({
      orderBy: {
        createdAt: "desc",
      },
    });
    // Convert Date objects to strings
    return games.map((game) => ({
      ...game,
      createdAt: game.createdAt.toISOString(),
    }));
  } catch (error) {
    console.error("Failed to fetch games:", error);
    // This is a server-side function, throwing an error is appropriate
    // The client will catch it and display a message.
    throw new Error("Could not retrieve games from the database.");
  }
}

export async function deleteGame(id: string) {
  try {
    const deletedGame = await prisma.game.delete({
      where: { id },
    });
    revalidatePath("/");
    return deletedGame;
  } catch (error) {
    console.error(`Failed to delete game ${id}:`, error);
    throw new Error("Could not delete the game from the database.");
  }
}
