// src/app/api/autorun/route.ts
import { NextResponse, type NextRequest } from "next/server";
import { Chess } from "chess.js";
import prisma from "@/lib/db";
import { generateMove } from "@/ai/flows/generate-chess-move";
// Note: SSE updates handled via database polling in spectate route

export const dynamic = "force-dynamic";

/**
 * Creates a new game and executes the first move for White.
 * The game then waits for subsequent moves to be triggered via the
 * /api/games/[gameId]/move endpoint.
 */
export async function POST(request: NextRequest) {
  console.log("AUTORUN: Received request to start a new game.");

  try {
    const text = await request.text();
    const body = text ? JSON.parse(text) : {};
    const whiteModel =
      body.whiteModel || process.env.AUTORUN_WHITE_MODEL || "gemini-2.0-flash";
    const blackModel =
      body.blackModel || process.env.AUTORUN_BLACK_MODEL || "gemini-2.5-pro";

    // 1. Create the game record in the database first
    const newGame = await prisma.game.create({
      data: {
        white: whiteModel,
        black: blackModel,
        pgn: "",
        status: "IN_PROGRESS",
        reasoningHistory: [],
      },
    });
    console.log(`AUTORUN: Created new game ${newGame.id}`);

    // 2. Immediately execute the first move for White
    const game = new Chess();
    const legalMoves = game.moves({ verbose: false });

    console.log(`AUTORUN: Generating first move for White (${whiteModel})`);
    const aiResult = await generateMove({
      fen: game.fen(),
      pgn: game.pgn(),
      player: "white",
      reasoningMode: true,
      model: whiteModel,
      legalMoves: legalMoves,
      isChess960: false,
    });

    if (!aiResult || !aiResult.move || !game.move(aiResult.move)) {
      console.error(`AUTORUN: AI failed to make a valid first move. Aborting.`);
      await prisma.game.update({
        where: { id: newGame.id },
        data: { status: "FAILED", result: "Aborted" },
      });
      return NextResponse.json(
        { error: "AI failed to make a valid first move." },
        { status: 500 },
      );
    }

    console.log(`AUTORUN: First move for White is ${aiResult.move}`);

    const reasoningHistory = [
      {
        moveNumber: 1,
        player: "white",
        move: aiResult.move,
        reason: aiResult.reason,
        analysis: aiResult.analysis,
        opponentPrediction: aiResult.opponentPrediction,
      },
    ];

    // 3. Update the game with the first move
    const updatedGame = await prisma.game.update({
      where: { id: newGame.id },
      data: {
        pgn: game.pgn(),
        reasoningHistory: reasoningHistory,
        lastMoveAt: new Date(),
      },
    });

    // 4. Send the update to any spectators
    // SSE updates handled by spectate route polling database

    // 5. Return the updated game object, now with one move made
    return NextResponse.json(updatedGame, { status: 201 }); // 201 Created
  } catch (error: any) {
    console.error("AUTORUN: Failed to create or start game.", error);
    // Check for specific AI-related errors like rate limiting
    if (error.message && error.message.includes("429")) {
      return new NextResponse(
        "AI model is rate-limited. Please try again later or use a different model.",
        { status: 429 },
      );
    }
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
