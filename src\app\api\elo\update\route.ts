import { NextRequest, NextResponse } from "next/server";
import { defaultELOService } from "@/lib/elo-service";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { whiteProfileId, blackProfileId, result } = body;

    if (!whiteProfileId || !blackProfileId || !result) {
      return NextResponse.json(
        { error: "whiteProfileId, blackProfileId, and result are required" },
        { status: 400 },
      );
    }

    if (!["white", "black", "draw"].includes(result)) {
      return NextResponse.json(
        { error: 'result must be "white", "black", or "draw"' },
        { status: 400 },
      );
    }

    const eloResult = await defaultELOService.updateEloRatings({
      matchId: "manual-update",
      whiteProfileId,
      blackProfileId,
      result,
    });

    return NextResponse.json(eloResult);
  } catch (error) {
    console.error("Error updating ELO ratings:", error);
    return NextResponse.json(
      { error: "Failed to update ELO ratings" },
      { status: 500 },
    );
  }
}
