import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/db";

export async function POST(request: NextRequest) {
  try {
    // Check if the request has content
    const contentLength = request.headers.get("content-length");
    if (!contentLength || contentLength === "0") {
      return NextResponse.json(
        { error: "Request body is required" },
        { status: 400 },
      );
    }

    let body;
    try {
      body = await request.json();
    } catch (jsonError) {
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 },
      );
    }
    const {
      type,
      whitePlayer,
      blackPlayer,
      whiteElo,
      blackElo,
      whiteEloChange,
      blackEloChange,
      result,
      move,
      reasoning,
      gameId,
      metadata,
    } = body;

    // Validate required fields
    if (!type || !whitePlayer || !blackPlayer) {
      return NextResponse.json(
        { error: "Missing required fields: type, whitePlayer, blackPlayer" },
        { status: 400 },
      );
    }

    // Create game activity record
    const activity = await db.gameActivity.create({
      data: {
        type,
        whitePlayer,
        blackPlayer,
        whiteElo,
        blackElo,
        whiteEloChange,
        blackEloChange,
        result,
        move,
        reasoning,
        gameId,
        metadata: metadata ? JSON.stringify(metadata) : undefined,
      },
    });

    return NextResponse.json(activity, { status: 201 });
  } catch (error) {
    console.error("Error creating game activity:", error);
    return NextResponse.json(
      { error: "Failed to create game activity" },
      { status: 500 },
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const type = searchParams.get("type");
    const gameId = searchParams.get("gameId");

    const where: any = {};
    if (type) where.type = type;
    if (gameId) where.gameId = gameId;

    const activities = await db.gameActivity.findMany({
      where,
      orderBy: { createdAt: "desc" },
      take: limit,
      skip: offset,
    });

    const total = await db.gameActivity.count({ where });

    return NextResponse.json({
      activities,
      total,
      limit,
      offset,
    });
  } catch (error) {
    console.error("Error fetching game activities:", error);
    return NextResponse.json(
      { error: "Failed to fetch game activities" },
      { status: 500 },
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const gameId = searchParams.get("gameId");
    const olderThan = searchParams.get("olderThan");

    const where: any = {};
    if (gameId) {
      where.gameId = gameId;
    }
    if (olderThan) {
      where.createdAt = {
        lt: new Date(olderThan),
      };
    }

    const result = await db.gameActivity.deleteMany({
      where,
    });

    return NextResponse.json({
      message: `Deleted ${result.count} game activities`,
      deletedCount: result.count,
    });
  } catch (error) {
    console.error("Error deleting game activities:", error);
    return NextResponse.json(
      { error: "Failed to delete game activities" },
      { status: 500 },
    );
  }
}
