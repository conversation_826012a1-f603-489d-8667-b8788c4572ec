// src/app/api/games/[gameId]/move/route.ts
import { NextResponse, type NextRequest } from "next/server";
import { Chess } from "chess.js";
import { GameStatus } from "@prisma/client";
import prisma from "@/lib/db";
import { generateMove } from "@/ai/flows/generate-chess-move";
// Note: SSE updates handled internally by spectate route

export const dynamic = "force-dynamic";

/**
 * Executes the next move in a given game.
 * This is triggered by the client to advance the game state.
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ gameId: string }> },
) {
  const { gameId } = await params;
  console.log(`MOVE: Received request to play next move for game ${gameId}`);

  try {
    // 1. Load the game from the database
    const existingGame = await prisma.game.findUnique({
      where: { id: gameId },
    });

    if (!existingGame) {
      return NextResponse.json({ error: "Game not found" }, { status: 404 });
    }

    if (existingGame.status !== "IN_PROGRESS") {
      return NextResponse.json(
        {
          error: `Game is not in progress. Current status: ${existingGame.status}`,
        },
        { status: 400 },
      );
    }

    // 2. Load game state into chess.js
    const game = new Chess();
    if (existingGame.pgn) {
      game.loadPgn(existingGame.pgn);
    }

    if (game.isGameOver()) {
      return NextResponse.json(
        { message: "Game is already over.", game: existingGame },
        { status: 200 },
      );
    }

    // 3. Generate move for the current player
    const player = game.turn() === "w" ? "white" : "black";
    const model = player === "white" ? existingGame.white : existingGame.black;

    console.log(`MOVE: Generating move for ${player} (${model})`);
    const aiResult = await generateMove({
      fen: game.fen(),
      pgn: game.pgn(),
      player: player,
      reasoningMode: true,
      model: model,
      legalMoves: game.moves({ verbose: false }),
      isChess960: false,
    });

    if (!aiResult || !aiResult.move || !game.move(aiResult.move)) {
      console.error(`MOVE: AI ${model} failed to make a valid move.`);
      // In a real scenario, you might want to mark the game as failed here
      return NextResponse.json(
        { error: "AI failed to make a valid move." },
        { status: 500 },
      );
    }

    console.log(`MOVE: ${player} played ${aiResult.move}`);

    // 4. Append new reasoning to history
    const currentHistory = (existingGame.reasoningHistory as any[]) || [];
    const newHistoryEntry = {
      moveNumber: game.history().length,
      player: player,
      move: aiResult.move,
      reason: aiResult.reason,
      analysis: aiResult.analysis,
      opponentPrediction: aiResult.opponentPrediction,
    };

    // 5. Check game status
    let gameStatus: GameStatus = GameStatus.IN_PROGRESS;
    let result = "*";
    if (game.isGameOver()) {
      gameStatus = GameStatus.COMPLETED;
      if (game.isCheckmate()) {
        result = game.turn() === "b" ? "1-0" : "0-1";
      } else {
        result = "1/2-1/2"; // Draw, stalemate, etc.
      }
    }

    // 6. Update the database
    const updatedGame = await prisma.game.update({
      where: { id: gameId },
      data: {
        pgn: game.pgn(),
        reasoningHistory: [...currentHistory, newHistoryEntry],
        lastMoveAt: new Date(),
        status: gameStatus,
        result: result,
      },
    });

    // 7. Note: SSE updates are handled by the spectate route watching the database

    // 8. Return the new game state
    return NextResponse.json(updatedGame);
  } catch (error: any) {
    console.error(`MOVE: Failed to execute move for game ${gameId}.`, error);
    if (error.message && error.message.includes("429")) {
      return new NextResponse(
        "AI model is rate-limited. Please try again later or use a different model.",
        { status: 429 },
      );
    }
    return new NextResponse("Internal Server Error", { status: 500 });
  }
}
