import { NextRequest, NextResponse } from "next/server";
import { GameStatusService } from "@/lib/game-status-service";

export async function GET(request: NextRequest) {
  try {
    const statistics = await GameStatusService.getGameStatistics();

    return NextResponse.json({
      ...statistics,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error fetching game statistics:", error);
    return NextResponse.json(
      { error: "Failed to fetch game statistics" },
      { status: 500 },
    );
  }
}
