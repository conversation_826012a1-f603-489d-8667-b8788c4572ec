import { NextRequest, NextResponse } from "next/server";
import {
  getMatchHistory,
  MatchHistoryFilters,
} from "@/lib/match-history-service";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const filters: MatchHistoryFilters = {
      profileId: searchParams.get("profileId") || undefined,
      tournamentId: searchParams.get("tournamentId") || undefined,
      result:
        (searchParams.get("result") as "win" | "loss" | "draw") || undefined,
      limit: parseInt(searchParams.get("limit") || "20"),
      offset: parseInt(searchParams.get("offset") || "0"),
    };

    // Parse date filters
    const dateFromStr = searchParams.get("dateFrom");
    const dateToStr = searchParams.get("dateTo");

    if (dateFromStr) {
      filters.dateFrom = new Date(dateFromStr);
    }

    if (dateToStr) {
      filters.dateTo = new Date(dateToStr);
    }

    const matches = await getMatchHistory(filters);
    return NextResponse.json(matches);
  } catch (error) {
    console.error("Error fetching match history:", error);
    return NextResponse.json(
      { error: "Failed to fetch match history" },
      { status: 500 },
    );
  }
}
