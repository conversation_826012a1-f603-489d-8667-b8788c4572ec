import { NextRequest, NextResponse } from "next/server";
import { getMatchStatistics } from "@/lib/match-history-service";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const profileId = searchParams.get("profileId");

    if (!profileId) {
      return NextResponse.json(
        { error: "Profile ID is required" },
        { status: 400 },
      );
    }

    const statistics = await getMatchStatistics(profileId);
    return NextResponse.json(statistics);
  } catch (error) {
    console.error("Error fetching match statistics:", error);
    return NextResponse.json(
      { error: "Failed to fetch match statistics" },
      { status: 500 },
    );
  }
}
