import { NextRequest, NextResponse } from "next/server";
import {
  getProfileWithRecentMatches,
  calculateProfileStatistics,
} from "@/lib/profile-service";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ profileId: string }> },
) {
  try {
    const { profileId } = await params;

    if (!profileId) {
      return NextResponse.json(
        { error: "Profile ID is required" },
        { status: 400 },
      );
    }

    // Get profile with recent matches and calculated statistics
    const profileWithStats = await getProfileWithRecentMatches(profileId, 10);

    if (!profileWithStats) {
      return NextResponse.json({ error: "Profile not found" }, { status: 404 });
    }

    // Calculate additional statistics
    const additionalStats = await calculateProfileStatistics(profileId);

    // Format the response
    const stats = {
      profileId: profileWithStats.id,
      name: profileWithStats.name,
      model: profileWithStats.model,
      eloRating: profileWithStats.eloRating,
      gamesPlayed: profileWithStats.gamesPlayed,
      wins: profileWithStats.wins,
      losses: profileWithStats.losses,
      draws: profileWithStats.draws,
      winRate: profileWithStats.winRate,
      averageOpponentElo: profileWithStats.averageOpponentElo,
      lastMatchAt: profileWithStats.lastMatchAt,
      isActive: profileWithStats.isActive,
      recentMatches: profileWithStats.recentMatches.map((match) => ({
        id: match.id,
        opponent: {
          name: match.opponent.name,
          model: match.opponent.model,
          eloRating: match.opponent.eloRating,
        },
        result: match.result,
        eloChange: match.eloChange,
        playedAs: match.playedAs,
        completedAt: match.completedAt,
        tournament: match.tournament
          ? {
              name: match.tournament.name,
              round: match.tournament.round,
            }
          : null,
      })),
      // Additional calculated stats
      calculatedStats: {
        winRate: additionalStats.winRate,
        averageOpponentElo: additionalStats.averageOpponentElo,
      },
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching profile stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch profile statistics" },
      { status: 500 },
    );
  }
}
