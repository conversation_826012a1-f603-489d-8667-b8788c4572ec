// src/app/api/spectate/[gameId]/route.ts
import { NextRequest } from "next/server";
import prisma, { withDatabaseRetry } from "@/lib/db";
// Enhanced SSE connection store with metadata
interface SSEConnection {
  writer: WritableStreamDefaultWriter;
  gameId: string;
  connected: boolean;
  lastHeartbeat: number;
}

const clients = new Map<string, SSEConnection>();

// Cleanup disconnected clients every 30 seconds
setInterval(() => {
  const now = Date.now();
  for (const [gameId, connection] of clients.entries()) {
    if (!connection.connected || now - connection.lastHeartbeat > 60000) {
      console.log(`Cleaning up stale connection for game ${gameId}`);
      try {
        connection.writer.close();
      } catch (e) {
        // Ignore close errors
      }
      clients.delete(gameId);
    }
  }
}, 30000);

// Internal function to send updates (not exported)
async function sendUpdateInternal(
  gameId: string,
  pgn: string,
  status: "IN_PROGRESS" | "COMPLETED" | "FAILED",
  aiResult?: any,
) {
  const connection = clients.get(gameId);
  if (!connection || !connection.connected) {
    console.log(`No active connection for game ${gameId}`);
    return;
  }

  console.log(`Sending update for game ${gameId}, status: ${status}`);

  try {
    const encoder = new TextEncoder();
    const data = JSON.stringify({
      type: "game_update",
      pgn,
      status,
      aiResult,
      timestamp: new Date().toISOString(),
    });

    await connection.writer.write(encoder.encode(`data: ${data}\n\n`));
    connection.lastHeartbeat = Date.now();

    console.log(`Successfully sent update for game ${gameId}`);
  } catch (error) {
    console.error(`Failed to write to stream for game ${gameId}:`, error);
    connection.connected = false;
    clients.delete(gameId);
    try {
      connection.writer.close();
    } catch (e) {
      // Ignore close errors
    }
  }
}

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ gameId: string }> },
) {
  const { gameId } = await params;

  if (!gameId) {
    console.log(`❌ SSE: Missing gameId`);
    return new Response("Game ID is required", { status: 400 });
  }

  console.log(`🔄 SSE: Setting up connection for game ${gameId}`);
  console.log(`📊 SSE: Current active connections: ${clients.size}`);

  // Clean up any existing connection for this game
  const existingConnection = clients.get(gameId);
  if (existingConnection) {
    console.log(`🧹 SSE: Cleaning up existing connection for game ${gameId}`);
    try {
      existingConnection.writer.close();
    } catch (e) {
      console.log(`⚠️ SSE: Error closing existing connection: ${e}`);
    }
    clients.delete(gameId);
  }

  const stream = new TransformStream();
  const writer = stream.writable.getWriter();

  const connection: SSEConnection = {
    writer,
    gameId,
    connected: true,
    lastHeartbeat: Date.now(),
  };

  clients.set(gameId, connection);

  // Track spectator (simplified tracking)
  console.log(`👁️ SSE: Adding spectator for game ${gameId}`);

  // Declare interval variables
  let heartbeatInterval: NodeJS.Timeout | undefined = undefined;
  let initialKeepAlive: NodeJS.Timeout | undefined = undefined;

  // Handle client disconnect
  const cleanup = () => {
    console.log(`🔌 SSE: Client disconnected for game ${gameId}`);
    console.log(`📊 SSE: Connections before cleanup: ${clients.size}`);
    connection.connected = false;
    clients.delete(gameId);
    console.log(`📊 SSE: Connections after cleanup: ${clients.size}`);

    // Remove spectator tracking
    console.log(`👁️ SSE: Removing spectator for game ${gameId}`);

    // Clear intervals
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = undefined;
      console.log(`🧹 SSE: Heartbeat interval cleared for game ${gameId}`);
    }
    if (initialKeepAlive) {
      clearInterval(initialKeepAlive);
      initialKeepAlive = undefined;
      console.log(`🧹 SSE: Keep-alive interval cleared for game ${gameId}`);
    }

    try {
      if (!writer.closed) {
        writer.close();
        console.log(`✅ SSE: Writer closed for game ${gameId}`);
      }
    } catch (e) {
      console.error(`❌ SSE: Error closing writer for game ${gameId}:`, e);
    }
  };

  req.signal.addEventListener("abort", cleanup);

  // Send initial connection message IMMEDIATELY to prevent timeout
  try {
    const encoder = new TextEncoder();

    // Validate gameId format (basic check)
    if (!gameId.match(/^[a-zA-Z0-9_-]+$/)) {
      throw new Error(`Invalid gameId format: ${gameId}`);
    }

    // Send immediate connection confirmation to prevent browser timeout
    if (connection.connected) {
      await writer.write(
        encoder.encode(
          `data: ${JSON.stringify({
            type: "connected",
            gameId,
            timestamp: new Date().toISOString(),
          })}\n\n`,
        ),
      );
    }

    console.log(`✅ SSE: Initial connection message sent for game ${gameId}`);

    // Send immediate heartbeat to keep connection alive
    if (connection.connected && !writer.closed) {
      await writer.write(
        encoder.encode(
          `data: ${JSON.stringify({
            type: "heartbeat",
            timestamp: new Date().toISOString(),
            gameId: gameId,
            connectionCount: clients.size,
          })}\n\n`,
        ),
      );
    }

    console.log(`💓 SSE: Initial heartbeat sent for game ${gameId}`);

    // Send initial game state with retry logic
    const game = await withDatabaseRetry(async () => {
      return await prisma.game.findUnique({ where: { id: gameId } });
    });

    if (game && connection.connected && !writer.closed) {
      await writer.write(
        encoder.encode(
          `data: ${JSON.stringify({
            type: "game_state",
            pgn: game.pgn || "",
            status: game.status || "IN_PROGRESS",
            reasoningHistory: game.reasoningHistory || [],
            timestamp: new Date().toISOString(),
          })}\n\n`,
        ),
      );
      console.log(`📊 SSE: Initial game state sent for game ${gameId}`);
    } else if (!game && connection.connected && !writer.closed) {
      await writer.write(
        encoder.encode(
          `data: ${JSON.stringify({
            type: "error",
            message: "Game not found",
            gameId,
            timestamp: new Date().toISOString(),
          })}\n\n`,
        ),
      );
      console.log(`❌ SSE: Game not found: ${gameId}`);
    }
  } catch (error) {
    console.error(`❌ SSE: Failed to send initial state for ${gameId}:`, error);
    connection.connected = false;
    clients.delete(gameId);

    // Try to send error message to client
    try {
      const encoder = new TextEncoder();
      if (!writer.closed) {
        await writer.write(
          encoder.encode(
            `data: ${JSON.stringify({
              type: "error",
              message: "Failed to initialize connection",
              error: error instanceof Error ? error.message : "Unknown error",
              timestamp: new Date().toISOString(),
            })}\n\n`,
          ),
        );
      }
    } catch (e) {
      console.error(`❌ SSE: Failed to send error message for ${gameId}:`, e);
    }
  }

  // Send heartbeat every 5 seconds (aggressive keep-alive)
  heartbeatInterval = setInterval(async () => {
    if (!connection.connected || (writer && writer.desiredSize === null)) {
      console.log(
        `💔 SSE: Heartbeat stopped for game ${gameId} - connection not active`,
      );
      clearInterval(heartbeatInterval);
      heartbeatInterval = undefined;
      return;
    }

    try {
      const encoder = new TextEncoder();
      if (connection.connected && !writer.closed) {
        await writer.write(
          encoder.encode(
            `data: ${JSON.stringify({
              type: "heartbeat",
              timestamp: new Date().toISOString(),
              gameId: gameId,
              connectionCount: clients.size,
              uptime: Date.now() - connection.lastHeartbeat,
            })}\n\n`,
          ),
        );
        connection.lastHeartbeat = Date.now();
        console.log(
          `💓 SSE: Heartbeat sent for game ${gameId}, active connections: ${clients.size}`,
        );
      }
    } catch (error) {
      console.error(`💔 SSE: Heartbeat failed for game ${gameId}:`, error);
      connection.connected = false;
      clients.delete(gameId);
      clearInterval(heartbeatInterval);
      heartbeatInterval = undefined;
    }
  }, 5000); // Aggressive 5-second heartbeat

  // Send immediate keep-alive every 2 seconds for first 30 seconds
  let keepAliveCount = 0;
  initialKeepAlive = setInterval(async () => {
    if (
      !connection.connected ||
      (writer && writer.desiredSize === null) ||
      keepAliveCount >= 15
    ) {
      clearInterval(initialKeepAlive);
      initialKeepAlive = undefined;
      return;
    }

    try {
      const encoder = new TextEncoder();
      if (connection.connected && !connection.writer.closed) {
        await connection.writer.write(
          encoder.encode(
            `data: ${JSON.stringify({
              type: "keep_alive",
              timestamp: new Date().toISOString(),
              gameId: gameId,
              sequence: keepAliveCount,
            })}\n\n`,
          ),
        );
        keepAliveCount++;
        console.log(
          `🔄 SSE: Keep-alive ${keepAliveCount} sent for game ${gameId}`,
        );
      }
    } catch (error) {
      console.error(`❌ SSE: Keep-alive failed for game ${gameId}:`, error);
      clearInterval(initialKeepAlive);
      initialKeepAlive = undefined;
    }
  }, 2000); // Every 2 seconds for first 30 seconds

  console.log(
    `✅ SSE: Response created for game ${gameId}, total connections: ${clients.size}`,
  );

  return new Response(stream.readable, {
    headers: {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache, no-store, must-revalidate",
      Connection: "keep-alive",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET",
      "Access-Control-Allow-Headers": "Cache-Control",
      "X-Accel-Buffering": "no", // Disable nginx buffering
      "X-Game-Id": gameId, // Add game ID to headers for debugging
    },
  });
}
