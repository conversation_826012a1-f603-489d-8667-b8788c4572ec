import { NextRequest, NextResponse } from "next/server";
import { TournamentStatus } from "@prisma/client";
import db from "@/lib/db";
import { MatchScheduler } from "@/lib/match-scheduler";
import { TournamentService } from "@/lib/tournament-service";

export const dynamic = "force-dynamic";

/**
 * Execute the next available match in a tournament
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ tournamentId: string }> },
) {
  try {
    const { tournamentId } = await params;

    // Validate tournament exists and is active
    const tournament = await TournamentService.getTournamentById(tournamentId);
    if (!tournament) {
      return NextResponse.json(
        { error: "Tournament not found" },
        { status: 404 },
      );
    }

    if (tournament.status !== TournamentStatus.ACTIVE) {
      return NextResponse.json(
        {
          error: `Cannot execute matches for tournament with status: ${tournament.status}`,
        },
        { status: 400 },
      );
    }

    // Check participant availability
    const participantIds = tournament.participants.map((p) => p.profileId);
    const availability =
      await TournamentService.checkParticipantAvailability(participantIds);

    const busyParticipants = Object.entries(availability)
      .filter(([_, available]) => !available)
      .map(([id]) => id);

    if (busyParticipants.length > 0) {
      const busyProfiles = tournament.participants
        .filter((p) => busyParticipants.includes(p.profileId))
        .map((p) => p.profile.name);

      return NextResponse.json(
        {
          error: "Some participants are currently busy",
          busyParticipants: busyProfiles,
          availableParticipants:
            participantIds.length - busyParticipants.length,
        },
        { status: 409 },
      );
    }

    // Execute next match
    const result = await MatchScheduler.executeNextScheduledMatch(tournamentId);

    if (!result) {
      // Check if tournament is complete
      const progress = await MatchScheduler.getTournamentProgress(tournamentId);

      if (progress.scheduledMatches === 0 && progress.inProgressMatches === 0) {
        return NextResponse.json({
          message: "No more matches to execute - tournament may be complete",
          progress,
        });
      }

      return NextResponse.json({
        message: "No matches available for execution at this time",
        progress,
      });
    }

    // Get updated tournament progress
    const progress = await MatchScheduler.getTournamentProgress(tournamentId);

    return NextResponse.json({
      message: "Match executed successfully",
      matchResult: result,
      progress,
    });
  } catch (error) {
    console.error("Error executing match:", error);

    return NextResponse.json(
      {
        error: "Failed to execute match",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
