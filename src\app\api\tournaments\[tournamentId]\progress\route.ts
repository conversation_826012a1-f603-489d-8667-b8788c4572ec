import { NextRequest, NextResponse } from "next/server";
import db from "@/lib/db";
import { MatchScheduler } from "@/lib/match-scheduler";
import { TournamentService } from "@/lib/tournament-service";
import { BackgroundJobSystem } from "@/lib/background-jobs";

export const dynamic = "force-dynamic";

/**
 * Get tournament progress and status information
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tournamentId: string }> },
) {
  try {
    const { tournamentId } = await params;

    // Validate tournament exists
    const tournament = await TournamentService.getTournamentById(tournamentId);
    if (!tournament) {
      return NextResponse.json(
        { error: "Tournament not found" },
        { status: 404 },
      );
    }

    // Get match progress
    const progress = await MatchScheduler.getTournamentProgress(tournamentId);

    // Get job status
    const jobStatus = BackgroundJobSystem.getJobStatus(tournamentId);

    // Get next scheduled matches
    const nextMatches = await TournamentService.getNextScheduledMatches(
      tournamentId,
      3,
    );

    // Calculate completion percentage
    const completionPercentage =
      progress.totalMatches > 0
        ? Math.round((progress.completedMatches / progress.totalMatches) * 100)
        : 0;

    // Get running matches info
    const runningMatches = MatchScheduler.getRunningMatches();

    return NextResponse.json({
      tournament: {
        id: tournament.id,
        name: tournament.name,
        status: tournament.status,
        format: tournament.format,
        participantCount: tournament.participants.length,
        matchInterval: tournament.matchInterval,
        timeWindow: tournament.timeWindow,
      },
      progress: {
        ...progress,
        completionPercentage,
      },
      jobStatus,
      nextMatches: nextMatches.map((match) => ({
        id: match.id,
        round: match.round,
        whitePlayer: (match as any).whiteProfile?.name || "TBD",
        blackPlayer: (match as any).blackProfile?.name || "TBD",
        scheduledAt: match.scheduledAt,
        status: match.status,
      })),
      runningMatches: runningMatches.length,
      systemStats: BackgroundJobSystem.getSystemStats(),
    });
  } catch (error) {
    console.error("Error getting tournament progress:", error);

    return NextResponse.json(
      {
        error: "Failed to get tournament progress",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
