import { describe, it, expect, beforeEach, vi, Mock } from "vitest";
import { NextRequest } from "next/server";
import { TournamentStatus, MatchStatus } from "@prisma/client";

// Mock dependencies
vi.mock("@/lib/db", () => ({
  db: {
    tournament: {
      findUnique: vi.fn(),
      update: vi.fn(),
    },
  },
}));

vi.mock("@/lib/tournament-service", () => ({
  TournamentService: {
    getTournamentById: vi.fn(),
    updateTournamentStatus: vi.fn(),
    checkParticipantAvailability: vi.fn(),
    getNextScheduledMatches: vi.fn(),
  },
}));

vi.mock("@/lib/background-jobs", () => ({
  BackgroundJobSystem: {
    startTournamentJob: vi.fn(),
    stopTournamentJob: vi.fn(),
    getJobStatus: vi.fn(),
    getSystemStats: vi.fn(),
  },
}));

vi.mock("@/lib/match-scheduler", () => ({
  MatchScheduler: {
    executeNextScheduledMatch: vi.fn(),
    getTournamentProgress: vi.fn(),
    getRunningMatches: vi.fn(),
  },
}));

vi.mock("@/lib/tournament-webhooks", () => ({
  TournamentEventEmitter: {
    tournamentStarted: vi.fn(),
    tournamentPaused: vi.fn(),
    tournamentResumed: vi.fn(),
  },
}));

import { TournamentService } from "@/lib/tournament-service";
import { BackgroundJobSystem } from "@/lib/background-jobs";
import { MatchScheduler } from "@/lib/match-scheduler";
import { TournamentEventEmitter } from "@/lib/tournament-webhooks";

// Import API handlers
import { POST as startTournament } from "../[tournamentId]/start/route";
import { POST as pauseTournament } from "../[tournamentId]/pause/route";
import { POST as resumeTournament } from "../[tournamentId]/resume/route";
import { POST as executeMatch } from "../[tournamentId]/execute-match/route";
import { GET as getProgress } from "../[tournamentId]/progress/route";

describe("Tournament API Endpoints", () => {
  const mockTournament = {
    id: "tournament-1",
    name: "Test Tournament",
    status: TournamentStatus.DRAFT,
    format: "ROUND_ROBIN",
    participants: [
      {
        profileId: "profile-1",
        profile: {
          id: "profile-1",
          name: "Model 1",
          model: "gemini-2.0-flash",
          eloRating: 1900,
        },
      },
      {
        profileId: "profile-2",
        profile: {
          id: "profile-2",
          name: "Model 2",
          model: "gemini-2.5-pro",
          eloRating: 1850,
        },
      },
    ],
    matches: [
      {
        id: "match-1",
        status: MatchStatus.SCHEDULED,
        round: "Round 1",
        whiteProfileId: "profile-1",
        blackProfileId: "profile-2",
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /tournaments/[tournamentId]/start", () => {
    it("should start a tournament successfully", async () => {
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        mockTournament,
      );
      (TournamentService.updateTournamentStatus as Mock).mockResolvedValue({
        ...mockTournament,
        status: TournamentStatus.ACTIVE,
      });
      (BackgroundJobSystem.startTournamentJob as Mock).mockResolvedValue(
        undefined,
      );
      (BackgroundJobSystem.getJobStatus as Mock).mockReturnValue({
        id: "tournament-1",
        status: "running",
        type: "tournament-scheduler",
      });

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/start",
        {
          method: "POST",
        },
      );

      const response = await startTournament(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe("Tournament started successfully");
      expect(data.tournament.status).toBe(TournamentStatus.ACTIVE);

      expect(TournamentService.updateTournamentStatus).toHaveBeenCalledWith(
        "tournament-1",
        TournamentStatus.ACTIVE,
      );
      expect(BackgroundJobSystem.startTournamentJob).toHaveBeenCalledWith(
        "tournament-1",
      );
      expect(TournamentEventEmitter.tournamentStarted).toHaveBeenCalledWith(
        "tournament-1",
        { name: "Test Tournament", participantCount: 2 },
      );
    });

    it("should return 404 for non-existent tournament", async () => {
      (TournamentService.getTournamentById as Mock).mockResolvedValue(null);

      const request = new NextRequest(
        "http://localhost/api/tournaments/non-existent/start",
        {
          method: "POST",
        },
      );

      const response = await startTournament(request, {
        params: Promise.resolve({ tournamentId: "non-existent" }),
      });
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toBe("Tournament not found");
    });

    it("should return 400 for tournament with wrong status", async () => {
      const activeTournament = {
        ...mockTournament,
        status: TournamentStatus.ACTIVE,
      };
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        activeTournament,
      );

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/start",
        {
          method: "POST",
        },
      );

      const response = await startTournament(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Cannot start tournament with status");
    });

    it("should return 400 for tournament with insufficient participants", async () => {
      const tournamentWithOneParticipant = {
        ...mockTournament,
        participants: [mockTournament.participants[0]],
      };
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        tournamentWithOneParticipant,
      );

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/start",
        {
          method: "POST",
        },
      );

      const response = await startTournament(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Tournament must have at least 2 participants");
    });

    it("should return 400 for tournament with no matches", async () => {
      const tournamentWithNoMatches = { ...mockTournament, matches: [] };
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        tournamentWithNoMatches,
      );

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/start",
        {
          method: "POST",
        },
      );

      const response = await startTournament(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe("Tournament has no scheduled matches");
    });
  });

  describe("POST /tournaments/[tournamentId]/pause", () => {
    it("should pause a tournament successfully", async () => {
      const activeTournament = {
        ...mockTournament,
        status: TournamentStatus.ACTIVE,
      };
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        activeTournament,
      );
      (TournamentService.updateTournamentStatus as Mock).mockResolvedValue({
        ...activeTournament,
        status: TournamentStatus.PAUSED,
      });
      (BackgroundJobSystem.getJobStatus as Mock).mockReturnValue({
        id: "tournament-1",
        status: "stopped",
        type: "tournament-scheduler",
      });

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/pause",
        {
          method: "POST",
        },
      );

      const response = await pauseTournament(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe("Tournament paused successfully");
      expect(data.tournament.status).toBe(TournamentStatus.PAUSED);

      expect(TournamentService.updateTournamentStatus).toHaveBeenCalledWith(
        "tournament-1",
        TournamentStatus.PAUSED,
      );
      expect(BackgroundJobSystem.stopTournamentJob).toHaveBeenCalledWith(
        "tournament-1",
      );
      expect(TournamentEventEmitter.tournamentPaused).toHaveBeenCalledWith(
        "tournament-1",
        { name: "Test Tournament" },
      );
    });

    it("should return 400 for tournament with wrong status", async () => {
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        mockTournament,
      );

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/pause",
        {
          method: "POST",
        },
      );

      const response = await pauseTournament(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain("Cannot pause tournament with status");
    });
  });

  describe("POST /tournaments/[tournamentId]/resume", () => {
    it("should resume a tournament successfully", async () => {
      const pausedTournament = {
        ...mockTournament,
        status: TournamentStatus.PAUSED,
      };
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        pausedTournament,
      );
      (TournamentService.updateTournamentStatus as Mock).mockResolvedValue({
        ...pausedTournament,
        status: TournamentStatus.ACTIVE,
      });
      (BackgroundJobSystem.startTournamentJob as Mock).mockResolvedValue(
        undefined,
      );
      (BackgroundJobSystem.getJobStatus as Mock).mockReturnValue({
        id: "tournament-1",
        status: "running",
        type: "tournament-scheduler",
      });

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/resume",
        {
          method: "POST",
        },
      );

      const response = await resumeTournament(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe("Tournament resumed successfully");
      expect(data.tournament.status).toBe(TournamentStatus.ACTIVE);

      expect(TournamentService.updateTournamentStatus).toHaveBeenCalledWith(
        "tournament-1",
        TournamentStatus.ACTIVE,
      );
      expect(BackgroundJobSystem.startTournamentJob).toHaveBeenCalledWith(
        "tournament-1",
      );
      expect(TournamentEventEmitter.tournamentResumed).toHaveBeenCalledWith(
        "tournament-1",
        { name: "Test Tournament" },
      );
    });
  });

  describe("POST /tournaments/[tournamentId]/execute-match", () => {
    it("should execute a match successfully", async () => {
      const activeTournament = {
        ...mockTournament,
        status: TournamentStatus.ACTIVE,
      };
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        activeTournament,
      );
      (
        TournamentService.checkParticipantAvailability as Mock
      ).mockResolvedValue({
        "profile-1": true,
        "profile-2": true,
      });
      (MatchScheduler.executeNextScheduledMatch as Mock).mockResolvedValue({
        matchId: "match-1",
        gameId: "game-1",
        result: "1-0",
        status: "COMPLETED",
        whiteEloChange: 10,
        blackEloChange: -10,
      });
      (MatchScheduler.getTournamentProgress as Mock).mockResolvedValue({
        totalMatches: 1,
        completedMatches: 1,
        inProgressMatches: 0,
        scheduledMatches: 0,
        failedMatches: 0,
      });

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/execute-match",
        {
          method: "POST",
        },
      );

      const response = await executeMatch(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe("Match executed successfully");
      expect(data.matchResult.status).toBe("COMPLETED");
      expect(data.progress.completedMatches).toBe(1);
    });

    it("should return 409 when participants are busy", async () => {
      const activeTournament = {
        ...mockTournament,
        status: TournamentStatus.ACTIVE,
      };
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        activeTournament,
      );
      (
        TournamentService.checkParticipantAvailability as Mock
      ).mockResolvedValue({
        "profile-1": false, // Busy
        "profile-2": true,
      });

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/execute-match",
        {
          method: "POST",
        },
      );

      const response = await executeMatch(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.error).toBe("Some participants are currently busy");
      expect(data.busyParticipants).toContain("Model 1");
    });
  });

  describe("GET /tournaments/[tournamentId]/progress", () => {
    it("should return tournament progress", async () => {
      const activeTournament = {
        ...mockTournament,
        status: TournamentStatus.ACTIVE,
      };
      (TournamentService.getTournamentById as Mock).mockResolvedValue(
        activeTournament,
      );
      (MatchScheduler.getTournamentProgress as Mock).mockResolvedValue({
        totalMatches: 1,
        completedMatches: 0,
        inProgressMatches: 1,
        scheduledMatches: 0,
        failedMatches: 0,
      });
      (BackgroundJobSystem.getJobStatus as Mock).mockReturnValue({
        id: "tournament-1",
        status: "running",
        type: "tournament-scheduler",
      });
      (TournamentService.getNextScheduledMatches as Mock).mockResolvedValue([]);
      (MatchScheduler.getRunningMatches as Mock).mockReturnValue(["profile-1"]);
      (BackgroundJobSystem.getSystemStats as Mock).mockReturnValue({
        totalJobs: 1,
        runningJobs: 1,
        stoppedJobs: 0,
        errorJobs: 0,
        runningMatches: 1,
      });

      const request = new NextRequest(
        "http://localhost/api/tournaments/tournament-1/progress",
        {
          method: "GET",
        },
      );

      const response = await getProgress(request, {
        params: Promise.resolve({ tournamentId: "tournament-1" }),
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.tournament.id).toBe("tournament-1");
      expect(data.progress.totalMatches).toBe(1);
      expect(data.progress.completionPercentage).toBe(0);
      expect(data.runningMatches).toBe(1);
    });
  });
});
