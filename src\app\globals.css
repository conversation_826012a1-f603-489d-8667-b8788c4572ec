@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 200 16% 94%;
    --foreground: 210 29% 20%;
    --card: 200 16% 100%;
    --card-foreground: 210 29% 20%;
    --popover: 200 16% 100%;
    --popover-foreground: 210 29% 20%;
    --primary: 210 29% 29%;
    --primary-foreground: 0 0% 98%;
    --secondary: 200 16% 90%;
    --secondary-foreground: 210 29% 29%;
    --muted: 200 16% 90%;
    --muted-foreground: 210 29% 40%;
    --accent: 218 51% 52%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 200 16% 88%;
    --input: 200 16% 88%;
    --ring: 218 51% 52%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 210 29% 10%;
    --foreground: 200 16% 94%;
    --card: 210 29% 15%;
    --card-foreground: 200 16% 94%;
    --popover: 210 29% 15%;
    --popover-foreground: 200 16% 94%;
    --primary: 200 16% 94%;
    --primary-foreground: 210 29% 15%;
    --secondary: 210 29% 20%;
    --secondary-foreground: 200 16% 94%;
    --muted: 210 29% 20%;
    --muted-foreground: 200 16% 70%;
    --accent: 218 51% 52%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 29% 20%;
    --input: 210 29% 20%;
    --ring: 218 51% 52%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  html {
    scroll-behavior: smooth;
  }

  ::selection {
    background-color: hsl(var(--accent) / 0.2);
  }

  :focus-visible {
    outline: none;
    box-shadow: 0 0 0 2px hsl(var(--ring));
  }
}

/* Custom scrollbar for modern browsers */
*::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
*::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}
*::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border));
  border-radius: 9999px;
  border: 2px solid transparent;
  background-clip: padding-box;
}
*::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground));
}
