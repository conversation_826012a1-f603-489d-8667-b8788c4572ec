import { NextRequest, NextResponse } from "next/server";
import { checkDatabaseHealth } from "@/lib/db";

export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Check database connectivity
    const isDatabaseHealthy = await checkDatabaseHealth();

    if (!isDatabaseHealthy) {
      return NextResponse.json(
        {
          status: "unhealthy",
          timestamp: new Date().toISOString(),
          checks: {
            database: "failed",
          },
          responseTime: Date.now() - startTime,
        },
        { status: 503 },
      );
    }

    // All checks passed
    return NextResponse.json(
      {
        status: "healthy",
        timestamp: new Date().toISOString(),
        checks: {
          database: "ok",
        },
        responseTime: Date.now() - startTime,
        environment: {
          nodeEnv: process.env.NODE_ENV,
          railwayEnvironment: process.env.RAILWAY_ENVIRONMENT || "unknown",
          deploymentId: process.env.RAILWAY_DEPLOYMENT_ID || "unknown",
        },
      },
      { status: 200 },
    );
  } catch (error) {
    console.error("[Health Check] Error:", error);

    return NextResponse.json(
      {
        status: "error",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error",
        responseTime: Date.now() - startTime,
      },
      { status: 500 },
    );
  }
}

// Support HEAD requests for simple health checks
export async function HEAD(request: NextRequest) {
  try {
    const isDatabaseHealthy = await checkDatabaseHealth();

    if (!isDatabaseHealthy) {
      return new NextResponse(null, { status: 503 });
    }

    return new NextResponse(null, { status: 200 });
  } catch (error) {
    return new NextResponse(null, { status: 500 });
  }
}
