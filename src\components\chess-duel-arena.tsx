"use client";

import { useState, useEffect, useCallback } from "react";
import { Chessboard } from "react-chessboard";
import type { Square } from "chess.js";
import { useToast } from "@/hooks/use-toast";
import { useChessGame } from "@/hooks/useChessGame";
import { useGameState, ALL_MODELS } from "@/hooks/useGameState";
import { useAIPlayer } from "@/hooks/useAIPlayer";
import { getGames, saveGame, deleteGame } from "@/app/actions/history";

import { GameHistory } from "./game/game-history";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "./ui/badge";
import {
  Bot,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Clock,
  Loader2,
  Pause,
  Play,
  PlayCircle,
  RotateCcw,
  Square as StopIconSquare,
  User,
  Wand2,
  XCircle,
} from "lucide-react";
import type { GenerateMoveOutput } from "@/ai/flows/generate-chess-move";

// Helper function to display simplified model names in the UI
const getModelDisplayName = (model: string): string => {
  if (!model.startsWith("openrouter/")) return model;
  const modelMap: Record<string, string> = {
    "openrouter/openai/gpt-oss-20b:free": "GPT OSS 20B",
    "openrouter/z-ai/glm-4.5-air:free": "GLM 4.5 Air",
    "openrouter/moonshotai/kimi-k2:free": "Kimi K2",
    "openrouter/google/gemma-3n-e2b-it:free": "Gemma 3N",
    "openrouter/deepseek/deepseek-r1-0528:free": "DeepSeek R1",
    "openrouter/qwen/qwen3-235b-a22b:free": "Qwen3 235B",
  };
  return modelMap[model] || model.replace("openrouter/", "");
};

const getGameStateDisplay = (state: any) => {
  switch (state) {
    case "idle":
      return {
        icon: <StopIconSquare className="h-3 w-3" />,
        text: "Ready",
        variant: "outline",
      };
    case "starting":
      return {
        icon: <Loader2 className="h-3 w-3 animate-spin" />,
        text: "Starting...",
        variant: "default",
      };
    case "running":
      return {
        icon: <Play className="h-3 w-3 animate-pulse" />,
        text: "🔴 LIVE",
        variant: "default",
      };
    case "paused":
      return {
        icon: <Pause className="h-3 w-3" />,
        text: "Paused",
        variant: "secondary",
      };
    case "processing-move":
      return {
        icon: <Clock className="h-3 w-3 animate-pulse" />,
        text: "Processing Move",
        variant: "default",
      };
    case "completed":
      return {
        icon: <CheckCircle className="h-3 w-3" />,
        text: "Completed",
        variant: "secondary",
      };
    case "error":
      return {
        icon: <XCircle className="h-3 w-3" />,
        text: "Error",
        variant: "destructive",
      };
    default:
      return {
        icon: <StopIconSquare className="h-3 w-3" />,
        text: "Unknown",
        variant: "outline",
      };
  }
};

type HistoryItem = {
  whiteMove: string;
  whiteReason?: string;
  whiteAnalysis?: GenerateMoveOutput["analysis"];
  whiteOpponentPrediction?: string;
  blackMove?: string;
  blackReason?: string;
  blackAnalysis?: GenerateMoveOutput["analysis"];
  blackOpponentPrediction?: string;
  moveNumber: number;
};

type GameHistoryEntry = {
  id: string;
  white: string;
  black: string;
  result: string | null;
  pgn: string;
  status: "IN_PROGRESS" | "COMPLETED" | "FAILED";
  createdAt: string;
};

export default function ChessDuelArena() {
  const { toast } = useToast();
  const {
    gameMode,
    setGameMode,
    humanPlayer,
    setHumanPlayer,
    whiteModel,
    setWhiteModel,
    blackModel,
    setBlackModel,
    isGameRunning,
    isGameStarted,
    gameStatus,
    setGameStatus,
    currentGameState,
    setCurrentGameState,
    logs,
    addLog,
    startGame,
    pauseGame,
    resumeGame,
    endGame,
    resetGameState,
  } = useGameState();

  const {
    game,
    fen,
    pgn,
    lastMove,
    currentMoveNumber,
    resetGame,
    processMove,
    loadPgn,
    navigateToMove,
    updateBoardState,
  } = useChessGame(gameMode);

  const { isThinking, makeAiMove } = useAIPlayer(addLog);

  const [history, setHistory] = useState<HistoryItem[]>([]);
  const [moveFrom, setMoveFrom] = useState<Square | null>(null);
  const [optionSquares, setOptionSquares] = useState({});
  const [gameHistory, setGameHistory] = useState<GameHistoryEntry[]>([]);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [spectatedGameId, setSpectatedGameId] = useState<string | null>(null);
  const [setupStep, setSetupStep] = useState(1);

  const handleEndGame = useCallback(
    async (status: string, result?: "1-0" | "0-1" | "1/2-1/2") => {
      endGame(status);
      if (game && result) {
        game.header("Result", result);
        updateBoardState();
        try {
          const whitePlayer = game.header().White || "Unknown";
          const blackPlayer = game.header().Black || "Unknown";
          await saveGame({
            white: whitePlayer,
            black: blackPlayer,
            result: result,
            pgn: game.pgn(),
            status: "COMPLETED",
            gameId: spectatedGameId || undefined,
          });
          addLog("Game saved to history.");
          toast({ title: "Game Saved" });
        } catch (error) {
          addLog(
            `ERROR_SAVING_GAME: ${error instanceof Error ? error.message : "Unknown error"}`,
          );
          toast({ variant: "destructive", title: "Failed to Save Game" });
        }
      }
    },
    [endGame, game, updateBoardState, spectatedGameId, addLog, toast],
  );

  const updateGameStatus = useCallback(() => {
    if (!game) return;
    let status = "";
    const turn = game.turn() === "b" ? "Black" : "White";

    if (game.isCheckmate()) {
      const winner = turn === "Black" ? "White" : "Black";
      status = `Checkmate! ${winner} wins.`;
      handleEndGame(status, winner === "White" ? "1-0" : "0-1");
    } else if (
      game.isDraw() ||
      game.isStalemate() ||
      game.isThreefoldRepetition() ||
      game.isInsufficientMaterial()
    ) {
      status = "Draw!";
      handleEndGame(status, "1/2-1/2");
    } else {
      const player = game.turn() === "w" ? "White" : "Black";
      const isHumanTurn =
        gameMode === "human-vs-ai" && game.turn() === humanPlayer[0];

      if (spectatedGameId) {
        status = `🔴 LIVE: Spectating ${player}'s turn`;
      } else if (isHumanTurn) {
        status = `🎯 Your turn! Move as ${player}`;
      } else if (isGameRunning) {
        status = `⚡ Game Active - ${player}'s turn (AI is thinking...)`;
      } else if (isGameStarted) {
        status = `⏸️ Game Paused - ${player}'s turn`;
      } else {
        status = `Waiting for game to start - ${player} to move first`;
      }
      setGameStatus(status);
    }
  }, [
    game,
    handleEndGame,
    gameMode,
    humanPlayer,
    isGameRunning,
    isGameStarted,
    spectatedGameId,
    setGameStatus,
  ]);

  const processAndLogMove = useCallback(
    (move: any, moveReasoning?: GenerateMoveOutput) => {
      const madeMove = processMove(move);
      if (madeMove) {
        addLog(`MOVE_SUCCESS: ${madeMove.san}.`);
        updateGameStatus();
        const currentPlayer = madeMove.color === "w" ? "white" : "black";
        setHistory((h) => {
          const newHistory = [...h];
          if (currentPlayer === "white") {
            return [
              ...newHistory,
              {
                moveNumber: game?.moveNumber() || 1,
                whiteMove: madeMove.san,
                whiteReason: moveReasoning?.reason ?? "Human move",
                whiteAnalysis: moveReasoning?.analysis,
                whiteOpponentPrediction: moveReasoning?.opponentPrediction,
              },
            ];
          } else {
            const lastItemIndex = newHistory.length - 1;
            if (lastItemIndex >= 0) {
              newHistory[lastItemIndex] = {
                ...newHistory[lastItemIndex],
                blackMove: madeMove.san,
                blackReason: moveReasoning?.reason ?? "Human move",
                blackAnalysis: moveReasoning?.analysis,
                blackOpponentPrediction: moveReasoning?.opponentPrediction,
              };
              return newHistory;
            }
          }
          return newHistory;
        });
        return true;
      }
      return false;
    },
    [processMove, addLog, updateGameStatus, game],
  );

  useEffect(() => {
    if (!game || !isGameRunning || game.isGameOver()) return;

    const isAITurn =
      gameMode === "ai-vs-ai" ||
      gameMode === "freestyle-ai-vs-ai" ||
      (gameMode === "human-vs-ai" && game.turn() !== humanPlayer[0]);

    if (isAITurn) {
      const timeoutId = setTimeout(async () => {
        setCurrentGameState("processing-move");
        const currentPlayer = game.turn() === "w" ? "white" : "black";
        const currentModel =
          currentPlayer === "white" ? whiteModel : blackModel;
        const aiResult = await makeAiMove(
          game,
          currentPlayer,
          currentModel,
          gameMode,
        );

        if (aiResult) {
          if (processAndLogMove(aiResult.move, aiResult)) {
            setCurrentGameState("running");
          } else {
            addLog(`ILLEGAL_MOVE_MAIN: AI move ${aiResult.move} was illegal.`);
            handleEndGame(
              `Error: AI provided illegal move.`,
              currentPlayer === "white" ? "0-1" : "1-0",
            );
          }
        } else {
          handleEndGame(
            `Error: AI failed to move.`,
            currentPlayer === "white" ? "0-1" : "1-0",
          );
        }
      }, 1200);
      return () => clearTimeout(timeoutId);
    }
  }, [
    isGameRunning,
    fen,
    game,
    gameMode,
    humanPlayer,
    whiteModel,
    blackModel,
    makeAiMove,
    processAndLogMove,
    handleEndGame,
    setCurrentGameState,
    addLog,
  ]);

  const onPieceDrop = (sourceSquare: Square, targetSquare: Square) => {
    if (!game) return false;
    const isHumanTurn =
      isGameRunning &&
      gameMode === "human-vs-ai" &&
      game.turn() === humanPlayer[0];
    if (!isHumanTurn) return false;

    const move = { from: sourceSquare, to: targetSquare, promotion: "q" };
    if (processAndLogMove(move)) {
      setMoveFrom(null);
      setOptionSquares({});
      return true;
    }
    toast({ variant: "destructive", title: "Invalid Move" });
    return false;
  };

  const onSquareClick = (square: Square) => {
    if (!game) return;
    const isHumanTurn =
      isGameRunning &&
      gameMode === "human-vs-ai" &&
      game.turn() === humanPlayer[0];
    if (!isHumanTurn) return;

    if (!moveFrom) {
      const moves = game.moves({ square, verbose: true });
      if (moves.length > 0) {
        setMoveFrom(square);
        const newOptions = moves.reduce((acc: any, move: any) => {
          acc[move.to] = { background: "rgba(255, 255, 0, 0.4)" };
          return acc;
        }, {});
        setOptionSquares(newOptions);
      }
    } else {
      if (square === moveFrom) {
        setMoveFrom(null);
        setOptionSquares({});
        return;
      }
      onPieceDrop(moveFrom, square);
    }
  };

  const handleReset = () => {
    resetGameState();
    resetGame();
    setHistory([]);
  };

  const handleStartStop = () => {
    if (isGameRunning) {
      pauseGame();
    } else {
      if (!isGameStarted) {
        handleReset();
        startGame();
        let startMessage = "Game started.";
        if (gameMode === "human-vs-ai")
          startMessage = `Game started. You are ${humanPlayer}.`;
        addLog(startMessage);
      } else {
        resumeGame();
      }
    }
    updateGameStatus();
  };

  const handleViewGame = (gameId: string) => {
    // For now, we'll just log the gameId since we need to fetch the full game data
    console.log("Viewing game:", gameId);
    // TODO: Implement game viewing functionality
  };

  const boardOrientation = gameMode === "human-vs-ai" ? humanPlayer : "white";

  const lastMoveStyle = lastMove
    ? {
        [lastMove.from]: { backgroundColor: "rgba(255, 255, 0, 0.4)" },
        [lastMove.to]: { backgroundColor: "rgba(255, 255, 0, 0.4)" },
      }
    : {};

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      <header className="text-center mb-4">
        <h1 className="text-3xl md:text-4xl font-bold text-primary mb-2">
          Chess Duel Arena
        </h1>
        <p className="text-sm text-muted-foreground">
          AI vs AI Chess Simulation
        </p>
      </header>

      <Card className="mb-4 shadow-lg">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Play Chess</CardTitle>
              <CardDescription>
                {isGameStarted
                  ? "Game in progress"
                  : setupStep === 1
                    ? "Select a game mode"
                    : "Configure your match"}
              </CardDescription>
            </div>
            <Badge
              variant={
                getGameStateDisplay(currentGameState).variant as
                  | "secondary"
                  | "destructive"
                  | "default"
                  | "outline"
                  | null
                  | undefined
              }
              className="flex items-center gap-1"
            >
              {getGameStateDisplay(currentGameState).icon}
              {getGameStateDisplay(currentGameState).text}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-4">
          {setupStep === 1 && !isGameStarted ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <Card
                className="cursor-pointer hover:shadow-md"
                onClick={() => {
                  setGameMode("human-vs-ai");
                  setSetupStep(2);
                }}
              >
                <CardHeader>
                  <User className="h-12 w-12 mx-auto text-primary" />
                </CardHeader>
                <CardContent>
                  <p className="font-semibold">Play vs AI</p>
                </CardContent>
              </Card>
              <Card
                className="cursor-pointer hover:shadow-md"
                onClick={() => {
                  setGameMode("ai-vs-ai");
                  setSetupStep(2);
                }}
              >
                <CardHeader>
                  <Bot className="h-12 w-12 mx-auto text-primary" />
                </CardHeader>
                <CardContent>
                  <p className="font-semibold">AI vs AI</p>
                </CardContent>
              </Card>
              <Card
                className="cursor-pointer hover:shadow-md"
                onClick={() => {
                  setGameMode("freestyle-ai-vs-ai");
                  setSetupStep(2);
                }}
              >
                <CardHeader>
                  <Wand2 className="h-12 w-12 mx-auto text-primary" />
                </CardHeader>
                <CardContent>
                  <p className="font-semibold">Freestyle AI vs AI</p>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <div>
                {gameMode === "human-vs-ai" && (
                  <div className="mb-4">
                    <Label>Your Color</Label>
                    <RadioGroup
                      value={humanPlayer}
                      onValueChange={(v) =>
                        setHumanPlayer(v as "white" | "black")
                      }
                      className="flex gap-4 mt-2"
                    >
                      <Label className="flex items-center gap-2 cursor-pointer">
                        <RadioGroupItem value="white" /> White
                      </Label>
                      <Label className="flex items-center gap-2 cursor-pointer">
                        <RadioGroupItem value="black" /> Black
                      </Label>
                    </RadioGroup>
                  </div>
                )}
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="white-model">White AI</Label>
                    <Select
                      value={whiteModel}
                      onValueChange={(v) => setWhiteModel(v as any)}
                    >
                      <SelectTrigger id="white-model">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {ALL_MODELS.map((m) => (
                          <SelectItem key={m} value={m}>
                            {getModelDisplayName(m)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="black-model">Black AI</Label>
                    <Select
                      value={blackModel}
                      onValueChange={(v) => setBlackModel(v as any)}
                    >
                      <SelectTrigger id="black-model">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {ALL_MODELS.map((m) => (
                          <SelectItem key={m} value={m}>
                            {getModelDisplayName(m)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
              <div className="flex flex-col gap-2 md:flex-row justify-end items-center pt-6">
                <Button
                  onClick={handleStartStop}
                  size="lg"
                  className="w-full md:w-auto"
                >
                  {isGameRunning ? (
                    <Pause className="mr-2 h-4 w-4" />
                  ) : (
                    <PlayCircle className="mr-2 h-4 w-4" />
                  )}
                  {isGameRunning
                    ? "Pause Game"
                    : isGameStarted
                      ? "Resume Game"
                      : "Start Game"}
                </Button>
                <Button
                  onClick={handleReset}
                  variant="outline"
                  size="lg"
                  className="w-full md:w-auto"
                >
                  <RotateCcw className="mr-2 h-4 w-4" /> Reset
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <div className="lg:col-span-2">
          <Card className="shadow-lg">
            <CardContent className="p-2">
              <div data-testid="chessboard">
                <Chessboard
                  position={fen}
                  onPieceDrop={onPieceDrop}
                  onSquareClick={onSquareClick}
                  customSquareStyles={{ ...optionSquares, ...lastMoveStyle }}
                  boardOrientation={boardOrientation}
                />
              </div>
            </CardContent>
            <CardHeader>
              <CardTitle className="text-center">{gameStatus}</CardTitle>
              <div className="flex justify-center items-center gap-4 mt-2">
                <Button
                  onClick={() => navigateToMove(currentMoveNumber - 1)}
                  disabled={currentMoveNumber === 0}
                >
                  <ChevronLeft /> Prev
                </Button>
                <span className="font-mono">
                  Move: {currentMoveNumber} / {game?.history().length ?? 0}
                </span>
                <Button
                  onClick={() => navigateToMove(currentMoveNumber + 1)}
                  disabled={currentMoveNumber === (game?.history().length ?? 0)}
                >
                  <ChevronRight /> Next
                </Button>
              </div>
            </CardHeader>
          </Card>
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Game Info</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {logs.slice(-5).map((log, index) => (
                  <div key={index} className="text-sm text-muted-foreground">
                    {log}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="mt-4">
        <GameHistory onViewGame={handleViewGame} />
      </div>
    </div>
  );
}
