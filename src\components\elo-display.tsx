"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Trophy,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Target,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface ELODisplayProps {
  rating: number;
  gamesPlayed?: number;
  eloChange?: number;
  opponentRating?: number;
  playerName?: string;
  className?: string;
}

// Calculate expected win probability using ELO formula
function calculateWinProbability(
  playerElo: number,
  opponentElo: number,
): number {
  return 1 / (1 + Math.pow(10, (opponentElo - playerElo) / 400));
}

// Determine volatility status based on games played
function getVolatilityStatus(gamesPlayed: number): {
  status: "volatile" | "stabilizing" | "stable";
  label: string;
  color: string;
} {
  if (gamesPlayed < 5) {
    return { status: "volatile", label: "Volatile", color: "text-red-500" };
  } else if (gamesPlayed < 10) {
    return {
      status: "stabilizing",
      label: "Stabilizing",
      color: "text-yellow-500",
    };
  } else {
    return { status: "stable", label: "Stable", color: "text-green-500" };
  }
}

export function ELODisplay({
  rating,
  gamesPlayed,
  eloChange,
  opponentRating,
  playerName,
  className,
}: ELODisplayProps) {
  const volatility =
    gamesPlayed !== undefined ? getVolatilityStatus(gamesPlayed) : null;
  const winProbability = opponentRating
    ? calculateWinProbability(rating, opponentRating)
    : null;

  return (
    <TooltipProvider>
      <div className={cn("space-y-1", className)}>
        {/* Main ELO Display */}
        <div className="flex items-center gap-2 p-2 bg-amber-50 rounded-lg">
          <div className="flex items-center gap-1">
            <Trophy className="h-4 w-4 text-amber-600" />
            <span className="font-bold text-lg">{rating}</span>
          </div>

          {/* ELO Change Indicator */}
          {eloChange !== undefined && eloChange !== 0 && (
            <div
              className={cn(
                "flex items-center gap-1 text-sm font-bold",
                eloChange > 0 ? "text-green-600" : "text-red-600",
              )}
            >
              {eloChange > 0 ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>
                {eloChange > 0 ? "+" : ""}
                {eloChange}
              </span>
            </div>
          )}

          {/* Volatility Status */}
          {volatility && (
            <Tooltip>
              <TooltipTrigger>
                <div className="flex items-center gap-1">
                  <AlertTriangle className={cn("h-3 w-3", volatility.color)} />
                  <Badge variant="outline" className="text-xs px-1 py-0">
                    {volatility.label}
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">
                  {volatility.status === "volatile" &&
                    "High rating volatility (< 5 games)"}
                  {volatility.status === "stabilizing" &&
                    "Rating stabilizing (5-9 games)"}
                  {volatility.status === "stable" &&
                    "Stable rating (10+ games)"}
                </p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>

        {/* Additional Info */}
        <div className="flex items-center gap-3 text-xs text-muted-foreground p-2 bg-gray-50 rounded">
          {/* Games Played */}
          {gamesPlayed !== undefined && (
            <div className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              <span>{gamesPlayed} games</span>
            </div>
          )}

          {/* Win Probability */}
          {winProbability !== null && (
            <Tooltip>
              <TooltipTrigger>
                <div className="flex items-center gap-1">
                  <TrendingUp className="h-3 w-3 text-blue-500" />
                  <span>{(winProbability * 100).toFixed(0)}% win chance</span>
                </div>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">
                  Expected win probability vs opponent ({opponentRating} ELO)
                </p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
}

// Simplified version for inline display
export function ELOBadge({
  rating,
  eloChange,
  className,
}: {
  rating: number;
  eloChange?: number;
  className?: string;
}) {
  return (
    <div
      className={cn(
        "flex items-center gap-1 px-2 py-1 bg-amber-50 rounded-full",
        className,
      )}
    >
      <Trophy className="h-3 w-3 text-amber-600" />
      <span className="font-bold text-sm">{rating}</span>
      {eloChange !== undefined && eloChange !== 0 && (
        <span
          className={cn(
            "text-xs font-bold",
            eloChange > 0 ? "text-green-600" : "text-red-600",
          )}
        >
          ({eloChange > 0 ? "+" : ""}
          {eloChange})
        </span>
      )}
    </div>
  );
}

export default ELODisplay;
