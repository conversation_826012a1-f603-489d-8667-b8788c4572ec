import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import {
  History,
  Eye,
  Clock,
  Trophy,
  RefreshCw,
  Loader2,
  Calendar,
  Target,
  CheckCircle,
  XCircle,
  ChevronLeft,
  ChevronRight,
  Trash2,
  MoreHorizontal,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { gameService, Game } from "@/lib/game-client";

interface GameHistoryProps {
  onViewGame?: (gameId: string) => void;
}

export function GameHistory({ onViewGame }: GameHistoryProps) {
  const [games, setGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [totalGames, setTotalGames] = useState(0);
  const { toast } = useToast();

  const GAMES_PER_PAGE = 20;

  const loadGames = useCallback(
    async (page = 0, append = false) => {
      try {
        if (!append) {
          setIsLoading(true);
          setError(null);
        }

        const response = await gameService.getCompletedGames(
          GAMES_PER_PAGE,
          page * GAMES_PER_PAGE,
        );

        if (append) {
          setGames((prev) => [...prev, ...response.games]);
        } else {
          setGames(response.games);
        }

        setHasMore(response.pagination.hasMore);
        setTotalGames(response.pagination.total);
        setCurrentPage(page);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "Failed to load game history";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    },
    [toast],
  );

  useEffect(() => {
    loadGames();
  }, [loadGames]);

  const handleLoadMore = () => {
    loadGames(currentPage + 1, true);
  };

  const handleRefresh = () => {
    loadGames(0, false);
  };

  const handleViewGame = (gameId: string) => {
    if (onViewGame) {
      onViewGame(gameId);
    }
    toast({
      title: "Loading Game",
      description: "Opening game details...",
    });
  };

  const handleDeleteGame = async (gameId: string, gameName: string) => {
    if (
      !window.confirm(
        `Are you sure you want to permanently delete the game "${gameName}"? This action cannot be undone.`,
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/games/${gameId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete game");
      }

      toast({
        title: "Game Deleted",
        description: "The game has been permanently deleted.",
      });

      // Remove the deleted game from local state
      setGames((prev) => prev.filter((game) => game.id !== gameId));
      setTotalGames((prev) => prev - 1);
    } catch (error) {
      console.error("Error deleting game:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to delete game",
        variant: "destructive",
      });
    }
  };

  const getResultBadge = (result?: string) => {
    switch (result) {
      case "1-0":
        return <Badge variant="default">White wins</Badge>;
      case "0-1":
        return <Badge variant="secondary">Black wins</Badge>;
      case "1/2-1/2":
        return <Badge variant="outline">Draw</Badge>;
      default:
        return <Badge variant="destructive">Incomplete</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "FAILED":
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString() +
      " " +
      date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })
    );
  };

  if (isLoading && games.length === 0) {
    return (
      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Game History
          </CardTitle>
          <CardDescription>Previously completed games</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading game history...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <div className="p-1 rounded-md bg-muted">
                <History className="h-5 w-5" />
              </div>
              Game History
              <Badge variant="outline" className="ml-2">
                {totalGames}
              </Badge>
            </CardTitle>
            <CardDescription>Previously completed games</CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="text-center py-4 text-red-600">
            <p>{error}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              className="mt-2"
            >
              Try Again
            </Button>
          </div>
        )}

        {!error && games.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <History className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">No completed games</p>
            <p className="text-sm">Completed games will appear here</p>
          </div>
        )}

        {!error && games.length > 0 && (
          <>
            <ScrollArea className="h-[500px]">
              <div className="space-y-3">
                {games.map((game, index) => (
                  <div key={game.id}>
                    <div className="p-4 rounded-lg border bg-card hover:bg-accent/50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            {getStatusIcon(game.status)}
                            {getResultBadge(game.result)}
                            {game.tournament && (
                              <Badge
                                variant="outline"
                                className="flex items-center gap-1"
                              >
                                <Trophy className="h-3 w-3" />
                                {game.tournament.name}
                              </Badge>
                            )}
                          </div>

                          <div className="flex items-center gap-2 mb-3">
                            <span className="font-medium text-sm truncate">
                              {game.white}
                            </span>
                            <span className="text-muted-foreground text-xs">
                              vs
                            </span>
                            <span className="font-medium text-sm truncate">
                              {game.black}
                            </span>
                          </div>

                          <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>
                                {gameService.formatDuration(game.duration)}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Target className="h-3 w-3" />
                              <span>{game.moveCount} moves</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              <span>{formatDate(game.createdAt)}</span>
                            </div>
                          </div>

                          {(game.whiteProfile || game.blackProfile) && (
                            <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                              {game.whiteProfile && (
                                <div className="flex items-center gap-1">
                                  <span>White:</span>
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {game.whiteProfile.eloRating} ELO
                                  </Badge>
                                </div>
                              )}
                              {game.blackProfile && (
                                <div className="flex items-center gap-1">
                                  <span>Black:</span>
                                  <Badge
                                    variant="secondary"
                                    className="text-xs"
                                  >
                                    {game.blackProfile.eloRating} ELO
                                  </Badge>
                                </div>
                              )}
                            </div>
                          )}
                        </div>

                        <div className="flex flex-col gap-1 ml-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewGame(game.id)}
                            className="flex items-center gap-1 h-8"
                          >
                            <Eye className="h-3 w-3" />
                            View
                          </Button>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() =>
                                  handleDeleteGame(
                                    game.id,
                                    `${game.white} vs ${game.black}`,
                                  )
                                }
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Game
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    </div>

                    {index < games.length - 1 && <Separator className="my-3" />}
                  </div>
                ))}
              </div>
            </ScrollArea>

            {hasMore && (
              <div className="flex justify-center mt-4">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={isLoading}
                  className="flex items-center gap-2"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                  Load More
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
