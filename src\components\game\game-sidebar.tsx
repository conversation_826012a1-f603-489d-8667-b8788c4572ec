"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import {
  Activity,
  RefreshCw,
  Play,
  Clock,
  Users,
  Eye,
  CheckCircle,
  XCircle,
  Pause,
  History,
  Trophy,
  Target,
  Calendar,
  Trash2,
  MoreHorizontal,
  StopCircle,
  Square,
  Loader2,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { gameService, Game } from "@/lib/game-client";

interface GameSidebarProps {
  className?: string;
  onSpectateGame?: (gameId: string) => void;
  onViewGame?: (gameId: string) => void;
  onStopGame?: (gameId: string) => void;
  onPauseGame?: (gameId: string) => void;
  currentSpectatedGameId?: string | null;
}

export function GameSidebar({
  className,
  onSpectateGame,
  onViewGame,
  onStopGame,
  onPauseGame,
  currentSpectatedGameId,
}: GameSidebarProps) {
  const [liveGames, setLiveGames] = useState<Game[]>([]);
  const [completedGames, setCompletedGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [activeTab, setActiveTab] = useState("live");
  const [gameStats, setGameStats] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [totalGames, setTotalGames] = useState(0);
  const { toast } = useToast();

  const GAMES_PER_PAGE = 20;

  const loadLiveGames = useCallback(async () => {
    try {
      const liveResponse = await gameService.getLiveGames(10);
      setLiveGames(liveResponse.games);

      // Load game statistics
      try {
        const statsResponse = await fetch("/api/games/statistics");
        if (statsResponse.ok) {
          const stats = await statsResponse.json();
          setGameStats(stats);
        }
      } catch (statsError) {
        console.warn("Failed to load game statistics:", statsError);
      }
    } catch (error) {
      console.error("Error loading live games:", error);
      toast({
        title: "Error",
        description: "Failed to load live games",
        variant: "destructive",
      });
    }
  }, [toast]);

  const loadCompletedGames = useCallback(
    async (page = 0, append = false) => {
      try {
        if (!append) {
          setIsLoading(true);
        }

        const response = await gameService.getCompletedGames(
          GAMES_PER_PAGE,
          page * GAMES_PER_PAGE,
        );

        if (append) {
          setCompletedGames((prev) => [...prev, ...response.games]);
        } else {
          setCompletedGames(response.games);
        }

        setHasMore(response.pagination.hasMore);
        setTotalGames(response.pagination.total);
        setCurrentPage(page);
      } catch (error) {
        console.error("Error loading completed games:", error);
        toast({
          title: "Error",
          description: "Failed to load game history",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    },
    [toast],
  );

  const loadAllGames = useCallback(async () => {
    setIsLoading(true);
    try {
      await Promise.all([loadLiveGames(), loadCompletedGames()]);
      setLastUpdate(new Date());
    } finally {
      setIsLoading(false);
    }
  }, [loadLiveGames, loadCompletedGames]);

  useEffect(() => {
    loadAllGames();

    // Auto-refresh every 30 seconds
    const interval = setInterval(loadLiveGames, 30000);
    return () => clearInterval(interval);
  }, [loadAllGames, loadLiveGames]);

  const handleLoadMore = () => {
    loadCompletedGames(currentPage + 1, true);
  };

  const handleDeleteGame = async (gameId: string, gameName: string) => {
    if (
      !window.confirm(
        `Are you sure you want to permanently delete the game "${gameName}"? This action cannot be undone.`,
      )
    ) {
      return;
    }

    try {
      const response = await fetch(`/api/games/${gameId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete game");
      }

      toast({
        title: "Game Deleted",
        description: "The game has been permanently deleted.",
      });

      // Remove from both live and completed games
      setLiveGames((prev) => prev.filter((game) => game.id !== gameId));
      setCompletedGames((prev) => prev.filter((game) => game.id !== gameId));
      setTotalGames((prev) => prev - 1);
    } catch (error) {
      console.error("Error deleting game:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to delete game",
        variant: "destructive",
      });
    }
  };

  const handleStopGame = async (gameId: string) => {
    if (!onStopGame) return;

    if (!window.confirm("Are you sure you want to stop this live game?")) {
      return;
    }

    try {
      onStopGame(gameId);
      toast({
        title: "Game Stopped",
        description: "The live game has been stopped.",
      });
    } catch (error) {
      console.error("Error stopping game:", error);
      toast({
        title: "Error",
        description: "Failed to stop the game",
        variant: "destructive",
      });
    }
  };

  const handlePauseGame = async (gameId: string) => {
    if (!onPauseGame) return;

    try {
      onPauseGame(gameId);
      toast({
        title: "Game Paused",
        description: "The live game has been paused.",
      });
    } catch (error) {
      console.error("Error pausing game:", error);
      toast({
        title: "Error",
        description: "Failed to pause the game",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "IN_PROGRESS":
        return <Play className="h-4 w-4 text-green-500" />;
      case "COMPLETED":
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case "FAILED":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Pause className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const badge = gameService.getGameStatusBadge(status);
    return (
      <Badge variant={badge.variant as any} className="text-xs">
        {badge.text}
      </Badge>
    );
  };

  const getResultBadge = (result?: string) => {
    switch (result) {
      case "1-0":
        return (
          <Badge variant="default" className="text-xs">
            White wins
          </Badge>
        );
      case "0-1":
        return (
          <Badge variant="secondary" className="text-xs">
            Black wins
          </Badge>
        );
      case "1/2-1/2":
        return (
          <Badge variant="outline" className="text-xs">
            Draw
          </Badge>
        );
      default:
        return (
          <Badge variant="destructive" className="text-xs">
            Incomplete
          </Badge>
        );
    }
  };

  const formatTimeAgo = (date: string) => {
    const now = new Date();
    const gameDate = new Date(date);
    const diffMs = now.getTime() - gameDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString() +
      " " +
      date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })
    );
  };

  return (
    <div className={className}>
      <Card className="h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2 text-lg">
                <div className="p-1 rounded-md bg-muted">
                  <Activity className="h-5 w-5" />
                </div>
                Game Center
              </CardTitle>
              <CardDescription>
                Live games and history
                {lastUpdate && (
                  <span className="ml-2 text-xs text-muted-foreground">
                    Updated: {lastUpdate.toLocaleTimeString()}
                  </span>
                )}
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadAllGames}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </CardHeader>

        <CardContent className="flex-1 p-0">
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="h-full"
          >
            <div className="px-6 pb-0">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="live" className="flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  Live ({liveGames.length})
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="flex items-center gap-2"
                >
                  <History className="h-4 w-4" />
                  History ({totalGames})
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Live Games Tab */}
            <TabsContent value="live" className="mt-0 px-6 pb-6">
              {liveGames.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="font-medium">No live games</p>
                  <p className="text-sm">Active games will appear here</p>
                </div>
              ) : (
                <ScrollArea className="h-[500px] pr-4">
                  <div className="space-y-3">
                    {liveGames.map((game) => (
                      <div
                        key={game.id}
                        className={`p-4 border rounded-lg hover:bg-muted/50 transition-colors ${
                          currentSpectatedGameId === game.id
                            ? "bg-primary/10 border-primary"
                            : "bg-card"
                        }`}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(game.status)}
                            {getStatusBadge(game.status)}
                            {currentSpectatedGameId === game.id && (
                              <Badge variant="default" className="text-xs">
                                <Eye className="h-3 w-3 mr-1" />
                                Watching
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="mb-3">
                          <div className="font-medium text-sm">
                            {game.white} vs {game.black}
                          </div>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground mt-2">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatTimeAgo(game.createdAt)}
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />0 watching
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {currentSpectatedGameId !== game.id &&
                            onSpectateGame && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onSpectateGame(game.id)}
                                className="flex items-center gap-1 h-8"
                              >
                                <Eye className="h-3 w-3" />
                                Watch
                              </Button>
                            )}

                          {/* Live game controls */}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {currentSpectatedGameId === game.id && (
                                <>
                                  <DropdownMenuItem
                                    onClick={() => handlePauseGame(game.id)}
                                    className="flex items-center gap-2"
                                  >
                                    <Pause className="h-4 w-4" />
                                    Pause Game
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => handleStopGame(game.id)}
                                    className="flex items-center gap-2 text-orange-600"
                                  >
                                    <StopCircle className="h-4 w-4" />
                                    Stop Game
                                  </DropdownMenuItem>
                                </>
                              )}
                              <DropdownMenuItem
                                onClick={() =>
                                  handleDeleteGame(
                                    game.id,
                                    `${game.white} vs ${game.black}`,
                                  )
                                }
                                className="text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Delete Game
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </TabsContent>

            {/* History Tab */}
            <TabsContent value="history" className="mt-0 px-6 pb-6">
              {completedGames.length === 0 ? (
                <div className="text-center py-12 text-muted-foreground">
                  <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p className="font-medium">No completed games</p>
                  <p className="text-sm">Finished games will appear here</p>
                </div>
              ) : (
                <>
                  <ScrollArea className="h-[500px] pr-4">
                    <div className="space-y-3">
                      {completedGames.map((game) => (
                        <div
                          key={game.id}
                          className="p-4 border rounded-lg hover:bg-muted/50 transition-colors bg-card"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-2 flex-wrap">
                              {getStatusIcon(game.status)}
                              {getResultBadge(game.result)}
                              {game.tournament && (
                                <Badge
                                  variant="outline"
                                  className="text-xs flex items-center gap-1"
                                >
                                  <Trophy className="h-3 w-3" />
                                  {game.tournament.name}
                                </Badge>
                              )}
                            </div>
                          </div>

                          <div className="mb-3">
                            <div className="font-medium text-sm">
                              {game.white} vs {game.black}
                            </div>
                            <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground mt-2">
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>
                                  {gameService.formatDuration(game.duration)}
                                </span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Target className="h-3 w-3" />
                                <span>{game.moveCount} moves</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>{formatDate(game.createdAt)}</span>
                              </div>
                            </div>

                            {(game.whiteProfile || game.blackProfile) && (
                              <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                {game.whiteProfile && (
                                  <div className="flex items-center gap-1">
                                    <span>White:</span>
                                    <Badge
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      {game.whiteProfile.eloRating} ELO
                                    </Badge>
                                  </div>
                                )}
                                {game.blackProfile && (
                                  <div className="flex items-center gap-1">
                                    <span>Black:</span>
                                    <Badge
                                      variant="secondary"
                                      className="text-xs"
                                    >
                                      {game.blackProfile.eloRating} ELO
                                    </Badge>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>

                          <div className="flex items-center gap-2">
                            {onViewGame && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onViewGame(game.id)}
                                className="flex items-center gap-1 h-8"
                              >
                                <Eye className="h-3 w-3" />
                                View
                              </Button>
                            )}

                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0"
                                >
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleDeleteGame(
                                      game.id,
                                      `${game.white} vs ${game.black}`,
                                    )
                                  }
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete Game
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>

                  {hasMore && (
                    <div className="flex justify-center mt-4">
                      <Button
                        variant="outline"
                        onClick={handleLoadMore}
                        disabled={isLoading}
                        className="flex items-center gap-2"
                      >
                        {isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="h-4 w-4" />
                        )}
                        Load More
                      </Button>
                    </div>
                  )}
                </>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
