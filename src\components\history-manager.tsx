"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LiveGamesDashboard } from "./live/live-games-dashboard";
import { GameActivitySidebar } from "./game/game-activity-sidebar";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Search,
  Filter,
  Download,
  Calendar as CalendarIcon,
  Trophy,
  TrendingUp,
  Users,
  Eye,
  ChevronLeft,
  ChevronRight,
  BarChart3,
  Award,
  History,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  MatchHistoryEntry,
  MatchHistoryFilters,
  MatchStatistics,
} from "@/lib/match-history-service";
import { profileService, LLMProfile } from "@/lib/profile-client";

interface HistoryManagerProps {
  className?: string;
  onSpectateGame?: (gameId: string) => void;
}

export function HistoryManager({
  className,
  onSpectateGame,
}: HistoryManagerProps) {
  const [matches, setMatches] = useState<MatchHistoryEntry[]>([]);
  const [profiles, setProfiles] = useState<LLMProfile[]>([]);
  const [statistics, setStatistics] = useState<MatchStatistics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<MatchHistoryFilters>({
    limit: 20,
    offset: 0,
  });
  const [selectedProfile, setSelectedProfile] = useState<string>("");
  const [dateFrom, setDateFrom] = useState<Date>();
  const [dateTo, setDateTo] = useState<Date>();
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const loadProfiles = useCallback(async () => {
    try {
      const profilesData = await profileService.getAllProfiles();
      setProfiles(profilesData);
    } catch (error) {
      console.error("Error loading profiles:", error);
    }
  }, []);

  const loadMatches = useCallback(async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (filters.profileId) params.append("profileId", filters.profileId);
      if (filters.tournamentId)
        params.append("tournamentId", filters.tournamentId);
      if (filters.result) params.append("result", filters.result);
      if (filters.dateFrom)
        params.append("dateFrom", filters.dateFrom.toISOString());
      if (filters.dateTo) params.append("dateTo", filters.dateTo.toISOString());
      if (filters.limit) params.append("limit", filters.limit.toString());
      if (filters.offset) params.append("offset", filters.offset.toString());

      const response = await fetch(`/api/matches?${params.toString()}`);
      if (!response.ok) throw new Error("Failed to fetch matches");

      const matchesData = await response.json();
      setMatches(matchesData);

      // Calculate total pages (simplified - in real app you'd get total count from API)
      const estimatedTotal =
        matchesData.length === filters.limit
          ? (currentPage + 1) * filters.limit!
          : currentPage * filters.limit!;
      setTotalPages(Math.ceil(estimatedTotal / filters.limit!));
    } catch (error) {
      console.error("Error loading matches:", error);
    } finally {
      setIsLoading(false);
    }
  }, [currentPage, filters]);

  const loadStatistics = useCallback(async () => {
    if (!selectedProfile) return;

    try {
      const response = await fetch(
        `/api/matches/statistics?profileId=${selectedProfile}`,
      );
      if (!response.ok) throw new Error("Failed to fetch statistics");

      const stats = await response.json();
      setStatistics(stats);
    } catch (error) {
      console.error("Error loading statistics:", error);
    }
  }, [selectedProfile]);

  useEffect(() => {
    loadProfiles();
    loadMatches();
  }, [loadProfiles, loadMatches]);

  useEffect(() => {
    loadMatches();
  }, [filters, loadMatches]);

  useEffect(() => {
    if (selectedProfile) {
      loadStatistics();
    }
  }, [selectedProfile, loadStatistics]);

  const handleFilterChange = (newFilters: Partial<MatchHistoryFilters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters, offset: 0 }));
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setFilters((prev) => ({ ...prev, offset: (page - 1) * prev.limit! }));
  };

  const getResultBadge = (
    result: string | null,
    isWhite: boolean,
    profileId?: string,
  ) => {
    if (!result) return <Badge variant="secondary">Unknown</Badge>;

    let variant: "default" | "secondary" | "destructive" | "outline" =
      "secondary";
    let text = "Draw";

    if (result === "1-0") {
      text = isWhite ? "Win" : "Loss";
      variant = isWhite ? "default" : "destructive";
    } else if (result === "0-1") {
      text = isWhite ? "Loss" : "Win";
      variant = isWhite ? "destructive" : "default";
    } else if (result === "1/2-1/2") {
      text = "Draw";
      variant = "secondary";
    }

    return <Badge variant={variant}>{text}</Badge>;
  };

  const formatEloChange = (change: number | null) => {
    if (change === null) return "—";
    const sign = change >= 0 ? "+" : "";
    const color = change >= 0 ? "text-green-600" : "text-red-600";
    return (
      <span className={color}>
        {sign}
        {change}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return (
      date.toLocaleDateString() +
      " " +
      date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })
    );
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10">
              <History className="w-6 h-6 text-primary" />
            </div>
            Game History & Live Matches
          </h1>
          <p className="text-muted-foreground mt-2">
            Watch live games, view match history, and analyze performance
            statistics
          </p>
        </div>
        <Button variant="outline" className="flex items-center gap-2">
          <Download className="w-4 h-4" />
          Export Data
        </Button>
      </div>

      <Tabs defaultValue="live" className="space-y-6">
        <div className="flex items-center gap-4">
          <TabsList className="grid grid-cols-4">
            <TabsTrigger value="live" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Live Games
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              Match History
            </TabsTrigger>
            <TabsTrigger value="statistics" className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4" />
              Statistics
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <Award className="w-4 h-4" />
              Analytics
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="live" className="space-y-6 mt-0">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <LiveGamesDashboard onSpectateGame={onSpectateGame} />
            </div>
            <div>
              <GameActivitySidebar />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="history" className="space-y-6 mt-0">
          {/* Filters */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Filter className="w-5 h-5" />
                Filters
              </CardTitle>
              <CardDescription>
                Refine your match history search
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label>Profile</Label>
                  <Select
                    value={selectedProfile}
                    onValueChange={(value) => {
                      const profileId = value === "all" ? "" : value;
                      setSelectedProfile(profileId);
                      handleFilterChange({ profileId: profileId || undefined });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All profiles" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All profiles</SelectItem>
                      {profiles.map((profile) => (
                        <SelectItem key={profile.id} value={profile.id}>
                          {profile.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Result</Label>
                  <Select
                    onValueChange={(value) => {
                      const result =
                        value === "all" ? undefined : (value as any);
                      handleFilterChange({ result });
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All results" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All results</SelectItem>
                      <SelectItem value="win">Wins</SelectItem>
                      <SelectItem value="loss">Losses</SelectItem>
                      <SelectItem value="draw">Draws</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>From Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full">
                        <CalendarIcon className="w-4 h-4 mr-2" />
                        {dateFrom
                          ? format(dateFrom, "MMM dd, yyyy")
                          : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={dateFrom}
                        onSelect={(date) => {
                          setDateFrom(date);
                          handleFilterChange({ dateFrom: date });
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div className="space-y-2">
                  <Label>To Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full">
                        <CalendarIcon className="w-4 h-4 mr-2" />
                        {dateTo
                          ? format(dateTo, "MMM dd, yyyy")
                          : "Select date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={dateTo}
                        onSelect={(date) => {
                          setDateTo(date);
                          handleFilterChange({ dateTo: date });
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Match History Table */}
          <Card className="shadow-sm">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Matches</CardTitle>
                  <CardDescription>
                    {matches.length} matches found
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </span>
                  <div className="flex gap-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-2 text-muted-foreground">
                      Loading matches...
                    </p>
                  </div>
                </div>
              ) : matches.length === 0 ? (
                <div className="text-center py-12">
                  <History className="w-12 h-12 mx-auto text-muted-foreground/50" />
                  <h3 className="mt-4 text-lg font-medium">No matches found</h3>
                  <p className="mt-1 text-muted-foreground">
                    Try adjusting your filters to see more results
                  </p>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[120px]">Date</TableHead>
                        <TableHead>Tournament</TableHead>
                        <TableHead>White Player</TableHead>
                        <TableHead>Black Player</TableHead>
                        <TableHead className="text-center">Result</TableHead>
                        <TableHead className="text-center">
                          ELO Changes
                        </TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {matches.map((match) => (
                        <TableRow key={match.id} className="hover:bg-muted/50">
                          <TableCell className="font-medium">
                            {match.completedAt
                              ? format(
                                  new Date(match.completedAt),
                                  "MMM dd, HH:mm",
                                )
                              : "—"}
                          </TableCell>
                          <TableCell>
                            {match.tournament ? (
                              <div className="flex items-center gap-2">
                                <Trophy className="w-4 h-4 text-yellow-500" />
                                <span className="font-medium">
                                  {match.tournament.name}
                                </span>
                              </div>
                            ) : (
                              <span className="text-muted-foreground">
                                Casual
                              </span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {match.whiteProfile.name}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                ELO: {match.whiteProfile.eloRating}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {match.blackProfile.name}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                ELO: {match.blackProfile.eloRating}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-center">
                            {getResultBadge(match.game.result, true)}
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="space-y-1">
                              <div className="text-xs">
                                W: {formatEloChange(match.whiteEloChange)}
                              </div>
                              <div className="text-xs">
                                B: {formatEloChange(match.blackEloChange)}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics" className="space-y-6 mt-0">
          <Card className="shadow-sm">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Statistics
                  </CardTitle>
                  <CardDescription>
                    Performance statistics for selected profile
                  </CardDescription>
                </div>
                <div className="w-64">
                  <Select
                    value={selectedProfile}
                    onValueChange={(value) => {
                      const profileId = value === "all" ? "" : value;
                      setSelectedProfile(profileId);
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a profile" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All profiles</SelectItem>
                      {profiles.map((profile) => (
                        <SelectItem key={profile.id} value={profile.id}>
                          {profile.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {!selectedProfile ? (
                <div className="text-center py-12">
                  <TrendingUp className="w-12 h-12 mx-auto text-muted-foreground/50" />
                  <h3 className="mt-4 text-lg font-medium">Select a profile</h3>
                  <p className="mt-1 text-muted-foreground">
                    Choose a profile to view detailed statistics
                  </p>
                </div>
              ) : statistics ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Total Matches
                      </CardTitle>
                      <BarChart3 className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {statistics.totalMatches}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Games played
                      </p>
                    </CardContent>
                  </Card>
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Win Rate
                      </CardTitle>
                      <Award className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-green-600">
                        {statistics.winRate.toFixed(1)}%
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Wins vs losses
                      </p>
                    </CardContent>
                  </Card>
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Avg ELO Change
                      </CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div
                        className={`text-2xl font-bold ${statistics.averageEloChange >= 0 ? "text-green-600" : "text-red-600"}`}
                      >
                        {statistics.averageEloChange > 0 ? "+" : ""}
                        {statistics.averageEloChange.toFixed(1)}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Per game
                      </p>
                    </CardContent>
                  </Card>
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">
                        Current Streak
                      </CardTitle>
                      <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">
                        {statistics.currentStreak.count}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1 capitalize">
                        {statistics.currentStreak.type} streak
                      </p>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-muted-foreground">
                    Loading statistics...
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6 mt-0">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="w-5 h-5" />
                Analytics
              </CardTitle>
              <CardDescription>
                Advanced analytics and performance trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <BarChart3 className="w-12 h-12 mx-auto text-muted-foreground/50" />
                <h3 className="mt-4 text-lg font-medium">
                  Analytics Dashboard
                </h3>
                <p className="mt-1 text-muted-foreground">
                  Advanced analytics and performance trends coming soon
                </p>
                <Button variant="outline" className="mt-4" disabled>
                  Coming Soon
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default HistoryManager;
