"use client";

import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Activity,
  RefreshCw,
  Play,
  Clock,
  Users,
  Eye,
  CheckCircle,
  XCircle,
  Pause,
} from "lucide-react";
import { gameService, Game } from "@/lib/game-client";

interface LiveGamesDashboardProps {
  className?: string;
  onSpectateGame?: (gameId: string) => void;
}

export function LiveGamesDashboard({
  className,
  onSpectateGame,
}: LiveGamesDashboardProps) {
  const [liveGames, setLiveGames] = useState<Game[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [gameStats, setGameStats] = useState<any>(null);

  const loadGames = async () => {
    setIsLoading(true);
    try {
      // Load live games only
      const liveResponse = await gameService.getLiveGames(10);
      setLiveGames(liveResponse.games);

      // Load game statistics
      try {
        const statsResponse = await fetch("/api/games/statistics");
        if (statsResponse.ok) {
          const stats = await statsResponse.json();
          setGameStats(stats);
        }
      } catch (statsError) {
        console.warn("Failed to load game statistics:", statsError);
      }

      setLastUpdate(new Date());
    } catch (error) {
      console.error("Error loading games:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadGames();

    // Auto-refresh every 30 seconds
    const interval = setInterval(loadGames, 30000);
    return () => clearInterval(interval);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "IN_PROGRESS":
        return <Play className="h-4 w-4 text-green-500" />;
      case "COMPLETED":
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case "FAILED":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Pause className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const badge = gameService.getGameStatusBadge(status);
    return (
      <Badge variant={badge.variant as any} className="text-xs">
        {badge.text}
      </Badge>
    );
  };

  const formatTimeAgo = (date: string) => {
    const now = new Date();
    const gameDate = new Date(date);
    const diffMs = now.getTime() - gameDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d ago`;
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <div className="p-1 rounded-md bg-muted">
                  <Activity className="h-5 w-5" />
                </div>
                Live Games
              </CardTitle>
              <CardDescription>
                Currently active games
                {lastUpdate && (
                  <span className="ml-2 text-xs text-muted-foreground">
                    Updated: {lastUpdate.toLocaleTimeString()}
                  </span>
                )}
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={loadGames}
              disabled={isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
              />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {liveGames.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No live games currently</p>
            </div>
          ) : (
            <ScrollArea className="h-64">
              <div className="space-y-3">
                {liveGames.map((game) => (
                  <div
                    key={game.id}
                    className="p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(game.status)}
                        <div>
                          <div className="font-medium text-sm">
                            {game.white} vs {game.black}
                          </div>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {formatTimeAgo(game.createdAt)}
                            </div>
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />0 watching
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(game.status)}
                        {onSpectateGame && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onSpectateGame(game.id)}
                            className="flex items-center gap-1 h-8"
                          >
                            <Eye className="h-3 w-3" />
                            Watch
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
