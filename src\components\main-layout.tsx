"use client";

import React, { useState, useEffect } from "react";
import { SidebarNavigation, ViewType } from "./sidebar-navigation";
import { ProfileManager } from "./profile/profile-manager";
import { TournamentManager } from "./tournament/tournament-manager";
import { HistoryManager } from "./history-manager";
import { GameActivitySidebar } from "./game/game-activity-sidebar";
import { LiveGamesDashboard } from "./live/live-games-dashboard";
import ChessDuelArena from "./chess-duel-arena";
import { profileService, LLMProfile } from "@/lib/profile-client";

export function MainLayout() {
  const [currentView, setCurrentView] = useState<ViewType>("game");
  const [profiles, setProfiles] = useState<LLMProfile[]>([]);

  useEffect(() => {
    const fetchProfiles = async () => {
      try {
        const profilesData = await profileService.getAllProfiles();
        setProfiles(profilesData);
      } catch (error) {
        console.error("Error fetching profiles:", error);
      }
    };

    fetchProfiles();
  }, []);

  const handleSpectateGame = (gameId: string) => {
    // Switch to the game view and pass the game ID to spectate
    setCurrentView("game");
    // We would need to implement the spectating functionality in ChessDuelArena
    // For now, we'll just switch to the game view
    console.log(`Spectating game: ${gameId}`);
  };

  const renderContent = () => {
    switch (currentView) {
      case "game":
        return (
          <div className="flex justify-center p-4">
            <div className="w-full max-w-5xl">
              <ChessDuelArena />
            </div>
          </div>
        );
      case "profiles":
        return (
          <div className="p-4">
            <div className="max-w-6xl mx-auto">
              <ProfileManager />
            </div>
          </div>
        );
      case "tournaments":
        return (
          <div className="p-4">
            <div className="max-w-6xl mx-auto">
              <TournamentManager profiles={profiles} />
            </div>
          </div>
        );
      case "history":
        return (
          <div className="p-4">
            <div className="max-w-6xl mx-auto">
              <HistoryManager onSpectateGame={handleSpectateGame} />
            </div>
          </div>
        );
      default:
        return (
          <div className="flex justify-center p-4">
            <div className="w-full max-w-5xl">
              <ChessDuelArena />
            </div>
          </div>
        );
    }
  };

  return (
    <SidebarNavigation
      currentView={currentView}
      onViewChange={setCurrentView}
      onSpectateGame={handleSpectateGame}
    >
      {renderContent()}
    </SidebarNavigation>
  );
}

export default MainLayout;
