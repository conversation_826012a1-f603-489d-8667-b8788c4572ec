"use client";

import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { RecentMatch } from "@/lib/profile-service";
import { Trophy, Calendar, Clock, Target } from "lucide-react";
import { cn } from "@/lib/utils";

interface RecentMatchesListProps {
  matches: RecentMatch[];
  title?: string;
  maxHeight?: string;
  showOpponentElo?: boolean;
}

export function RecentMatchesList({
  matches,
  title = "Recent Matches",
  maxHeight = "400px",
  showOpponentElo = true,
}: RecentMatchesListProps) {
  const getResultColor = (result: "win" | "loss" | "draw") => {
    switch (result) {
      case "win":
        return "text-green-600 bg-green-50 border-green-200";
      case "loss":
        return "text-red-600 bg-red-50 border-red-200";
      case "draw":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getResultIcon = (result: "win" | "loss" | "draw") => {
    switch (result) {
      case "win":
        return "✓";
      case "loss":
        return "✗";
      case "draw":
        return "=";
      default:
        return "-";
    }
  };

  const getResultText = (result: "win" | "loss" | "draw") => {
    switch (result) {
      case "win":
        return "Win";
      case "loss":
        return "Loss";
      case "draw":
        return "Draw";
      default:
        return "Unknown";
    }
  };

  const formatEloChange = (change: number) => {
    const sign = change >= 0 ? "+" : "";
    return `${sign}${change}`;
  };

  const getModelDisplayName = (model: string) => {
    if (model.startsWith("openrouter/")) {
      const parts = model.split("/");
      return parts[parts.length - 1].replace(":free", "");
    }
    return model;
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } else if (diffInHours < 24 * 7) {
      return date.toLocaleDateString([], {
        weekday: "short",
        month: "short",
        day: "numeric",
      });
    } else {
      return date.toLocaleDateString([], {
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    }
  };

  if (matches.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Trophy className="h-5 w-5" />
            <span>{title}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Trophy className="h-12 w-12 mx-auto mb-4 opacity-30" />
            <p>No matches played yet</p>
            <p className="text-sm">
              Matches will appear here once games are completed
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Trophy className="h-5 w-5" />
            <span>{title}</span>
          </div>
          <Badge variant="secondary">{matches.length}</Badge>
        </CardTitle>
        <CardDescription>Latest match results and ELO changes</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <ScrollArea style={{ maxHeight }}>
          <div className="space-y-0">
            {matches.map((match, index) => (
              <div key={match.id}>
                <div className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    {/* Left side - Result and Opponent */}
                    <div className="flex items-center space-x-3">
                      <div
                        className={cn(
                          "flex items-center justify-center w-8 h-8 rounded-full border font-semibold text-sm",
                          getResultColor(match.result),
                        )}
                      >
                        {getResultIcon(match.result)}
                      </div>

                      <div className="flex flex-col">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-sm">
                            {getModelDisplayName(match.opponent.model)}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {match.playedAs}
                          </Badge>
                        </div>

                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          {showOpponentElo && (
                            <>
                              <Target className="h-3 w-3" />
                              <span>ELO {match.opponent.eloRating}</span>
                            </>
                          )}
                          {match.tournament && (
                            <>
                              <span>•</span>
                              <span>{match.tournament.name}</span>
                              {match.tournament.round && (
                                <span>({match.tournament.round})</span>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Right side - ELO Change and Date */}
                    <div className="flex items-center space-x-3">
                      <div className="text-right">
                        <div
                          className={cn(
                            "font-semibold text-sm",
                            match.eloChange >= 0
                              ? "text-green-600"
                              : "text-red-600",
                          )}
                        >
                          {formatEloChange(match.eloChange)}
                        </div>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="text-xs text-gray-500 cursor-help">
                                {formatDate(match.completedAt)}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>{match.completedAt.toLocaleString()}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                  </div>
                </div>
                {index < matches.length - 1 && <Separator />}
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
