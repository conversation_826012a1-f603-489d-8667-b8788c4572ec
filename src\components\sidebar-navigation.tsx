"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  <PERSON>bar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar";
import { Users, Trophy, History, Play } from "lucide-react";
import ThemeToggle from "@/components/ui/theme-toggle";

export type ViewType = "profiles" | "tournaments" | "history" | "game";

interface SidebarNavigationProps {
  currentView: ViewType;
  onViewChange: (view: ViewType) => void;
  className?: string;
  children?: React.ReactNode;
  onSpectateGame?: (gameId: string) => void;
}

const navigationItems = [
  {
    id: "game" as ViewType,
    label: "Live Game",
    icon: Play,
    description: "Watch AI vs AI chess matches",
  },
  {
    id: "profiles" as ViewType,
    label: "Profiles",
    icon: Users,
    description: "Manage LLM profiles and ELO ratings",
  },
  {
    id: "tournaments" as ViewType,
    label: "Tournaments",
    icon: Trophy,
    description: "Create and manage tournaments",
  },
  {
    id: "history" as ViewType,
    label: "History",
    icon: History,
    description: "View match history and analytics",
  },
];

function SidebarNavigationContent({
  currentView,
  onViewChange,
}: SidebarNavigationProps) {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  return (
    <Sidebar variant="inset" collapsible="icon">
      <SidebarHeader className="border-b border-sidebar-border">
        <div className="flex items-center gap-2 px-4 py-2">
          <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
            <Trophy className="size-4" />
          </div>
          {!isCollapsed && (
            <div className="grid flex-1 text-left text-sm leading-tight">
              <span className="truncate font-semibold">Chess Arena</span>
              <span className="truncate text-xs text-sidebar-muted-foreground">
                AI Tournament System
              </span>
            </div>
          )}
          {isCollapsed ? null : (
            <div className="ml-auto">
              <ThemeToggle />
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent>
        <SidebarMenu>
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = currentView === item.id;

            return (
              <SidebarMenuItem key={item.id}>
                <SidebarMenuButton
                  onClick={() => onViewChange(item.id)}
                  isActive={isActive}
                  tooltip={isCollapsed ? item.label : undefined}
                  className="w-full hover:bg-sidebar-accent transition-colors"
                >
                  <Icon className="size-4" />
                  <span>{item.label}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border">
        <div className="p-2">
          {!isCollapsed && (
            <div className="text-xs text-sidebar-muted-foreground px-2 py-1 flex items-center justify-between">
              <span>AI Chess Tournament</span>
              <span>v1.2.0</span>
            </div>
          )}
        </div>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}

export function SidebarNavigation(props: SidebarNavigationProps) {
  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full">
        <SidebarNavigationContent {...props} />
        <main className="flex-1 flex flex-col">
          <header className="sticky top-0 z-20 flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/75">
            <div className="flex w-full max-w-7xl mx-auto items-center gap-2">
              <SidebarTrigger className="-ml-1 hover:bg-muted rounded-md p-1" />
              <h1 className="text-lg font-semibold flex items-center gap-2">
                <Trophy className="h-5 w-5 text-primary" />
                {navigationItems.find((item) => item.id === props.currentView)
                  ?.label || "Chess Arena"}
              </h1>
              <div className="ml-auto flex items-center gap-2">
                <ThemeToggle />
              </div>
            </div>
          </header>
          <div className="flex-1 p-4 overflow-auto bg-gradient-to-br from-background to-muted/30">
            <div className="w-full max-w-7xl mx-auto">{props.children}</div>
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
}

export default SidebarNavigation;
