"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Trophy,
  Users,
  Clock,
  Play,
  CheckCircle,
  XCircle,
  Minus,
} from "lucide-react";
import { TournamentWithDetails } from "@/lib/tournament-service";
import { TournamentFormat, MatchStatus } from "@prisma/client";

interface TournamentBracketProps {
  tournament: TournamentWithDetails;
}

export function TournamentBracket({ tournament }: TournamentBracketProps) {
  const getMatchStatusIcon = (status: MatchStatus) => {
    switch (status) {
      case MatchStatus.SCHEDULED:
        return <Clock className="w-4 h-4 text-gray-500" />;
      case MatchStatus.IN_PROGRESS:
        return <Play className="w-4 h-4 text-blue-500" />;
      case MatchStatus.COMPLETED:
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case MatchStatus.FAILED:
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Minus className="w-4 h-4 text-gray-400" />;
    }
  };

  const getMatchStatusColor = (status: MatchStatus) => {
    switch (status) {
      case MatchStatus.SCHEDULED:
        return "bg-gray-500";
      case MatchStatus.IN_PROGRESS:
        return "bg-blue-500";
      case MatchStatus.COMPLETED:
        return "bg-green-500";
      case MatchStatus.FAILED:
        return "bg-red-500";
      default:
        return "bg-gray-400";
    }
  };

  const formatDateTime = (date: Date | null) => {
    if (!date) return "Not scheduled";
    return new Intl.DateTimeFormat("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date));
  };

  const renderRoundRobinBracket = () => {
    const matches = tournament.matches;
    const rounds = [...new Set(matches.map((m) => m.round))].sort();

    return (
      <div className="space-y-6">
        {rounds.map((round) => {
          const roundMatches = matches.filter((m) => m.round === round);

          return (
            <div key={round} className="space-y-3">
              <h3 className="text-lg font-semibold flex items-center space-x-2">
                <Trophy className="w-5 h-5" />
                <span>{round}</span>
                <Badge variant="outline">
                  {
                    roundMatches.filter(
                      (m) => m.status === MatchStatus.COMPLETED,
                    ).length
                  }
                  /{roundMatches.length} completed
                </Badge>
              </h3>

              <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                {roundMatches.map((match) => (
                  <Card
                    key={match.id}
                    className="hover:shadow-md transition-shadow"
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <Badge className={getMatchStatusColor(match.status)}>
                          {match.status}
                        </Badge>
                        {getMatchStatusIcon(match.status)}
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-white border-2 border-gray-800 rounded-sm"></div>
                            <span className="font-medium text-sm">
                              {match.whiteProfile.name}
                            </span>
                          </div>
                          {match.whiteEloChange && (
                            <Badge
                              variant="outline"
                              className={
                                match.whiteEloChange > 0
                                  ? "text-green-600"
                                  : "text-red-600"
                              }
                            >
                              {match.whiteEloChange > 0 ? "+" : ""}
                              {match.whiteEloChange}
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center justify-center">
                          <span className="text-xs text-muted-foreground">
                            vs
                          </span>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="w-3 h-3 bg-gray-800 rounded-sm"></div>
                            <span className="font-medium text-sm">
                              {match.blackProfile.name}
                            </span>
                          </div>
                          {match.blackEloChange && (
                            <Badge
                              variant="outline"
                              className={
                                match.blackEloChange > 0
                                  ? "text-green-600"
                                  : "text-red-600"
                              }
                            >
                              {match.blackEloChange > 0 ? "+" : ""}
                              {match.blackEloChange}
                            </Badge>
                          )}
                        </div>
                      </div>

                      <div className="mt-3 pt-3 border-t text-xs text-muted-foreground">
                        {match.status === MatchStatus.SCHEDULED &&
                          match.scheduledAt && (
                            <div>
                              Scheduled: {formatDateTime(match.scheduledAt)}
                            </div>
                          )}
                        {match.status === MatchStatus.COMPLETED &&
                          match.completedAt && (
                            <div>
                              Completed: {formatDateTime(match.completedAt)}
                            </div>
                          )}
                        {match.status === MatchStatus.IN_PROGRESS && (
                          <div className="text-blue-600">
                            Match in progress...
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderEliminationBracket = () => {
    const matches = tournament.matches;
    const rounds = [...new Set(matches.map((m) => m.round))].sort();

    return (
      <div className="space-y-8">
        {rounds.map((round) => {
          const roundMatches = matches.filter((m) => m.round === round);

          return (
            <div key={round} className="space-y-4">
              <h3 className="text-lg font-semibold text-center flex items-center justify-center space-x-2">
                <Trophy className="w-5 h-5" />
                <span>{round}</span>
                <Badge variant="outline">
                  {
                    roundMatches.filter(
                      (m) => m.status === MatchStatus.COMPLETED,
                    ).length
                  }
                  /{roundMatches.length}
                </Badge>
              </h3>

              <div className="flex justify-center">
                <div
                  className="grid gap-4"
                  style={{
                    gridTemplateColumns: `repeat(${Math.max(1, roundMatches.length)}, 1fr)`,
                  }}
                >
                  {roundMatches.map((match) => (
                    <Card
                      key={match.id}
                      className="w-64 hover:shadow-md transition-shadow"
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-3">
                          <Badge className={getMatchStatusColor(match.status)}>
                            {match.status}
                          </Badge>
                          {getMatchStatusIcon(match.status)}
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                            <div className="flex items-center space-x-2">
                              <div className="w-3 h-3 bg-white border-2 border-gray-800 rounded-sm"></div>
                              <span className="font-medium text-sm">
                                {match.whiteProfileId === "TBD"
                                  ? "TBD"
                                  : match.whiteProfile.name}
                              </span>
                            </div>
                            {match.whiteEloChange && (
                              <Badge
                                variant="outline"
                                className={
                                  match.whiteEloChange > 0
                                    ? "text-green-600"
                                    : "text-red-600"
                                }
                              >
                                {match.whiteEloChange > 0 ? "+" : ""}
                                {match.whiteEloChange}
                              </Badge>
                            )}
                          </div>

                          <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
                            <div className="flex items-center space-x-2">
                              <div className="w-3 h-3 bg-gray-800 rounded-sm"></div>
                              <span className="font-medium text-sm">
                                {match.blackProfileId === "TBD"
                                  ? "TBD"
                                  : match.blackProfile.name}
                              </span>
                            </div>
                            {match.blackEloChange && (
                              <Badge
                                variant="outline"
                                className={
                                  match.blackEloChange > 0
                                    ? "text-green-600"
                                    : "text-red-600"
                                }
                              >
                                {match.blackEloChange > 0 ? "+" : ""}
                                {match.blackEloChange}
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="mt-3 pt-3 border-t text-xs text-muted-foreground">
                          {match.status === MatchStatus.SCHEDULED &&
                            match.scheduledAt && (
                              <div>
                                Scheduled: {formatDateTime(match.scheduledAt)}
                              </div>
                            )}
                          {match.status === MatchStatus.COMPLETED &&
                            match.completedAt && (
                              <div>
                                Completed: {formatDateTime(match.completedAt)}
                              </div>
                            )}
                          {match.status === MatchStatus.IN_PROGRESS && (
                            <div className="text-blue-600">
                              Match in progress...
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  const renderStandings = () => {
    if (tournament.format !== TournamentFormat.ROUND_ROBIN) {
      return null;
    }

    // Calculate standings for round-robin
    const standings = tournament.participants
      .map((participant) => {
        const participantMatches = tournament.matches.filter(
          (match) =>
            (match.whiteProfileId === participant.profileId ||
              match.blackProfileId === participant.profileId) &&
            match.status === MatchStatus.COMPLETED,
        );

        let wins = 0;
        let losses = 0;
        let draws = 0;

        // This is a simplified calculation - in a real implementation,
        // you'd get the actual game results from the Game model
        participantMatches.forEach((match) => {
          // For now, we'll use ELO changes to infer results
          const isWhite = match.whiteProfileId === participant.profileId;
          const eloChange = isWhite
            ? match.whiteEloChange
            : match.blackEloChange;

          if (eloChange && eloChange > 0) wins++;
          else if (eloChange && eloChange < 0) losses++;
          else draws++;
        });

        return {
          participant,
          wins,
          losses,
          draws,
          points: wins + draws * 0.5,
          matches: participantMatches.length,
        };
      })
      .sort((a, b) => b.points - a.points);

    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Trophy className="w-5 h-5" />
            <span>Standings</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {standings.map((standing, index) => (
              <div
                key={standing.participant.id}
                className="flex items-center justify-between p-3 bg-muted/50 rounded"
              >
                <div className="flex items-center space-x-3">
                  <Badge
                    variant="outline"
                    className="w-8 h-8 rounded-full flex items-center justify-center"
                  >
                    {index + 1}
                  </Badge>
                  <div>
                    <div className="font-medium">
                      {standing.participant.profile.name}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {standing.participant.profile.model}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{standing.points} pts</div>
                  <div className="text-sm text-muted-foreground">
                    {standing.wins}W-{standing.losses}L-{standing.draws}D
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Tournament Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Users className="w-6 h-6 mx-auto mb-2 text-blue-600" />
            <div className="text-2xl font-bold">
              {tournament.participants.length}
            </div>
            <div className="text-sm text-muted-foreground">Participants</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Trophy className="w-6 h-6 mx-auto mb-2 text-yellow-600" />
            <div className="text-2xl font-bold">
              {tournament.matches.length}
            </div>
            <div className="text-sm text-muted-foreground">Total Matches</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <CheckCircle className="w-6 h-6 mx-auto mb-2 text-green-600" />
            <div className="text-2xl font-bold">
              {
                tournament.matches.filter(
                  (m) => m.status === MatchStatus.COMPLETED,
                ).length
              }
            </div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <Clock className="w-6 h-6 mx-auto mb-2 text-orange-600" />
            <div className="text-2xl font-bold">
              {
                tournament.matches.filter(
                  (m) => m.status === MatchStatus.SCHEDULED,
                ).length
              }
            </div>
            <div className="text-sm text-muted-foreground">Pending</div>
          </CardContent>
        </Card>
      </div>

      {/* Standings (for round-robin) */}
      {renderStandings()}

      {/* Bracket Visualization */}
      <Card>
        <CardHeader>
          <CardTitle>Tournament Bracket</CardTitle>
        </CardHeader>
        <CardContent>
          {tournament.format === TournamentFormat.ROUND_ROBIN
            ? renderRoundRobinBracket()
            : renderEliminationBracket()}
        </CardContent>
      </Card>
    </div>
  );
}
