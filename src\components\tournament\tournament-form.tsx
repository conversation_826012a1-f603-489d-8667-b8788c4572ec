"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { X, Users, Trophy, Clock, Calendar } from "lucide-react";
import { TournamentFormat } from "@prisma/client";
import { CreateTournamentInput } from "@/lib/tournament-service";

interface LLMProfile {
  id: string;
  name: string;
  model: string;
  eloRating: number;
  isActive: boolean;
}

interface TournamentFormProps {
  profiles: LLMProfile[];
  onSubmit: (tournament: CreateTournamentInput) => void;
  onCancel: () => void;
}

export function TournamentForm({
  profiles,
  onSubmit,
  onCancel,
}: TournamentFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    format: "" as TournamentFormat,
    participantIds: [] as string[],
    matchInterval: 30,
    timeWindow: "09:00-17:00",
    scheduledStart: "",
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = "Tournament name is required";
    }

    if (!formData.format) {
      newErrors.format = "Tournament format is required";
    }

    if (formData.participantIds.length < 2) {
      newErrors.participants = "At least 2 participants are required";
    }

    if (formData.matchInterval < 1 || formData.matchInterval > 1440) {
      newErrors.matchInterval =
        "Match interval must be between 1 and 1440 minutes";
    }

    if (!isValidTimeWindow(formData.timeWindow)) {
      newErrors.timeWindow = "Invalid time window format (use HH:MM-HH:MM)";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    const tournamentData: CreateTournamentInput = {
      name: formData.name.trim(),
      format: formData.format,
      participantIds: formData.participantIds,
      matchInterval: formData.matchInterval,
      timeWindow: formData.timeWindow,
      scheduledStart: formData.scheduledStart
        ? new Date(formData.scheduledStart)
        : undefined,
    };

    onSubmit(tournamentData);
  };

  const isValidTimeWindow = (timeWindow: string): boolean => {
    const timeRegex =
      /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]-([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(timeWindow);
  };

  const handleParticipantToggle = (profileId: string, checked: boolean) => {
    setFormData((prev) => ({
      ...prev,
      participantIds: checked
        ? [...prev.participantIds, profileId]
        : prev.participantIds.filter((id) => id !== profileId),
    }));

    if (errors.participants) {
      setErrors((prev) => ({ ...prev, participants: "" }));
    }
  };

  const getFormatDescription = (format: TournamentFormat) => {
    switch (format) {
      case TournamentFormat.ROUND_ROBIN:
        return "Every participant plays against every other participant once";
      case TournamentFormat.SINGLE_ELIMINATION:
        return "Single-elimination bracket where losers are eliminated immediately";
      case TournamentFormat.DOUBLE_ELIMINATION:
        return "Double-elimination bracket with winners and losers brackets";
      default:
        return "";
    }
  };

  const getEstimatedMatches = () => {
    const n = formData.participantIds.length;
    if (n < 2) return 0;

    switch (formData.format) {
      case TournamentFormat.ROUND_ROBIN:
        return (n * (n - 1)) / 2;
      case TournamentFormat.SINGLE_ELIMINATION:
        return n - 1;
      case TournamentFormat.DOUBLE_ELIMINATION:
        return (n - 1) * 2 - 1;
      default:
        return 0;
    }
  };

  const getEstimatedDuration = () => {
    const matches = getEstimatedMatches();
    const totalMinutes = matches * formData.matchInterval;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    if (hours > 0) {
      return `~${hours}h ${minutes}m`;
    }
    return `~${minutes}m`;
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Trophy className="w-5 h-5" />
                <span>Create Tournament</span>
              </CardTitle>
              <CardDescription>
                Set up a new tournament between LLM models
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onCancel}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Tournament Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Tournament Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, name: e.target.value }))
                }
                placeholder="Enter tournament name"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name}</p>
              )}
            </div>

            {/* Tournament Format */}
            <div className="space-y-2">
              <Label htmlFor="format">Tournament Format</Label>
              <Select
                value={formData.format}
                onValueChange={(value: TournamentFormat) =>
                  setFormData((prev) => ({ ...prev, format: value }))
                }
              >
                <SelectTrigger
                  className={errors.format ? "border-red-500" : ""}
                >
                  <SelectValue placeholder="Select tournament format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={TournamentFormat.ROUND_ROBIN}>
                    Round Robin
                  </SelectItem>
                  <SelectItem value={TournamentFormat.SINGLE_ELIMINATION}>
                    Single Elimination
                  </SelectItem>
                  <SelectItem value={TournamentFormat.DOUBLE_ELIMINATION}>
                    Double Elimination
                  </SelectItem>
                </SelectContent>
              </Select>
              {formData.format && (
                <p className="text-sm text-muted-foreground">
                  {getFormatDescription(formData.format)}
                </p>
              )}
              {errors.format && (
                <p className="text-sm text-red-500">{errors.format}</p>
              )}
            </div>

            {/* Participants */}
            <div className="space-y-3">
              <Label>
                Participants ({formData.participantIds.length} selected)
              </Label>
              {profiles.length === 0 ? (
                <p className="text-sm text-muted-foreground">
                  No active profiles available. Create some LLM profiles first.
                </p>
              ) : (
                <div className="space-y-2 max-h-48 overflow-y-auto border rounded-md p-3">
                  {profiles.map((profile) => (
                    <div
                      key={profile.id}
                      className="flex items-center space-x-3"
                    >
                      <Checkbox
                        id={profile.id}
                        checked={formData.participantIds.includes(profile.id)}
                        onCheckedChange={(checked) =>
                          handleParticipantToggle(
                            profile.id,
                            checked as boolean,
                          )
                        }
                      />
                      <Label
                        htmlFor={profile.id}
                        className="flex-1 cursor-pointer"
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <span className="font-medium">{profile.name}</span>
                            <span className="text-sm text-muted-foreground ml-2">
                              {profile.model}
                            </span>
                          </div>
                          <Badge variant="outline">
                            {profile.eloRating} ELO
                          </Badge>
                        </div>
                      </Label>
                    </div>
                  ))}
                </div>
              )}
              {errors.participants && (
                <p className="text-sm text-red-500">{errors.participants}</p>
              )}
            </div>

            {/* Match Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="matchInterval">Match Interval (minutes)</Label>
                <Input
                  id="matchInterval"
                  type="number"
                  min="1"
                  max="1440"
                  value={formData.matchInterval}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      matchInterval: parseInt(e.target.value) || 30,
                    }))
                  }
                  className={errors.matchInterval ? "border-red-500" : ""}
                />
                {errors.matchInterval && (
                  <p className="text-sm text-red-500">{errors.matchInterval}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="timeWindow">Daily Time Window</Label>
                <Input
                  id="timeWindow"
                  value={formData.timeWindow}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      timeWindow: e.target.value,
                    }))
                  }
                  placeholder="09:00-17:00"
                  className={errors.timeWindow ? "border-red-500" : ""}
                />
                {errors.timeWindow && (
                  <p className="text-sm text-red-500">{errors.timeWindow}</p>
                )}
              </div>
            </div>

            {/* Scheduled Start (Optional) */}
            <div className="space-y-2">
              <Label htmlFor="scheduledStart">Scheduled Start (Optional)</Label>
              <Input
                id="scheduledStart"
                type="datetime-local"
                value={formData.scheduledStart}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    scheduledStart: e.target.value,
                  }))
                }
              />
              <p className="text-sm text-muted-foreground">
                Leave empty to start manually
              </p>
            </div>

            {/* Tournament Summary */}
            {formData.participantIds.length >= 2 && formData.format && (
              <Card className="bg-muted/50">
                <CardContent className="pt-4">
                  <h4 className="font-medium mb-3">Tournament Summary</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span>{formData.participantIds.length} participants</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Trophy className="w-4 h-4 text-muted-foreground" />
                      <span>{getEstimatedMatches()} matches</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <span>{getEstimatedDuration()} duration</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-muted-foreground" />
                      <span>{formData.timeWindow}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700"
                disabled={formData.participantIds.length < 2}
              >
                Create Tournament
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
