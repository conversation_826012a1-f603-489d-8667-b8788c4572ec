import { useState, useCallback } from "react";
import {
  generateMove,
  GenerateMoveOutput,
} from "@/ai/flows/generate-chess-move";
import type { Chess } from "chess.js";
import type { Player, Model } from "./useGameState";
import type { GameMode } from "./useChessGame";

export const useAIPlayer = (addLog: (message: string) => void) => {
  const [isThinking, setIsThinking] = useState(false);

  const makeAiMove = useCallback(
    async (
      game: Chess,
      player: Player,
      model: Model,
      gameMode: GameMode,
    ): Promise<(GenerateMoveOutput & { san: string }) | null> => {
      setIsThinking(true);
      addLog(`Thinking... (${player} as ${model})`);

      try {
        let attempts = 0;
        const maxAttempts = 5;
        while (attempts < maxAttempts) {
          attempts++;
          addLog(`Attempt ${attempts}/${maxAttempts} for ${player}...`);
          try {
            const legalMoves = game.moves({ verbose: false });
            const result = await generateMove({
              fen: game.fen(),
              pgn: game.pgn(),
              player: player,
              reasoningMode: true,
              model: model,
              legalMoves: legalMoves,
              isChess960: gameMode === "freestyle-ai-vs-ai",
            });

            if (!result || !result.move) {
              addLog(
                `INVALID_RESPONSE: AI (${model}) returned an invalid response. Retrying...`,
              );
              await new Promise((resolve) => setTimeout(resolve, 1000));
              continue;
            }

            const moveResult = game.move(result.move);
            game.undo(); // We only want to validate the move, not make it here.

            if (moveResult) {
              addLog(
                `AI (${model}) proposed move: ${result.move}. Reason: ${result.reason}`,
              );
              return { ...result, san: moveResult.san };
            } else {
              addLog(
                `ILLEGAL_MOVE: Invalid move from ${model}: ${result.move}. Retrying...`,
              );
              await new Promise((resolve) => setTimeout(resolve, 1000));
            }
          } catch (error) {
            addLog(
              `ERROR attempt ${attempts}/${maxAttempts}: ${error instanceof Error ? error.message : String(error)}`,
            );
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }
        addLog(
          `AI (${model}) failed to provide a valid move after ${maxAttempts} attempts.`,
        );
        return null;
      } catch (error) {
        addLog(
          `FATAL_ERROR in makeAiMove: ${error instanceof Error ? error.message : String(error)}`,
        );
        return null;
      } finally {
        setIsThinking(false);
      }
    },
    [addLog],
  );

  return { isThinking, makeAiMove };
};
