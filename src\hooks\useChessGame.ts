import { useState, useCallback, useRef, useMemo } from "react";
import { Chess } from "chess.js";
import type { Square, Move } from "chess.js";

// Re-exporting necessary types
export type { Chess as ChessInstance, Square, Move };

export type GameMode = "ai-vs-ai" | "human-vs-ai" | "freestyle-ai-vs-ai";

// Utility function to generate a Chess960 FEN
const generateChess960Fen = (): string => {
  const backRank: (string | null)[] = Array(8).fill(null);

  // Place bishops on opposite colored squares
  const lightSquares = [0, 2, 4, 6];
  const darkSquares = [1, 3, 5, 7];
  const bishop1Pos = lightSquares.splice(
    Math.floor(Math.random() * lightSquares.length),
    1,
  )[0];
  const bishop2Pos = darkSquares.splice(
    Math.floor(Math.random() * darkSquares.length),
    1,
  )[0];
  backRank[bishop1Pos] = "B";
  backRank[bishop2Pos] = "B";

  // Place queen
  let emptySquares = backRank
    .map((p, i) => (p === null ? i : -1))
    .filter((i) => i !== -1);
  const queenPos = emptySquares.splice(
    Math.floor(Math.random() * emptySquares.length),
    1,
  )[0];
  backRank[queenPos] = "Q";

  // Place knights
  const knight1Pos = emptySquares.splice(
    Math.floor(Math.random() * emptySquares.length),
    1,
  )[0];
  backRank[knight1Pos] = "N";
  const knight2Pos = emptySquares.splice(
    Math.floor(Math.random() * emptySquares.length),
    1,
  )[0];
  backRank[knight2Pos] = "N";

  // Place rooks and king
  const finalEmptySquares = backRank
    .map((p, i) => (p === null ? i : -1))
    .filter((i) => i !== -1);
  finalEmptySquares.sort((a, b) => a - b);
  backRank[finalEmptySquares[0]] = "R";
  backRank[finalEmptySquares[1]] = "K";
  backRank[finalEmptySquares[2]] = "R";

  const whiteRank = backRank.join("");
  const blackRank = whiteRank.toLowerCase();

  const fen = `${blackRank}/pppppppp/8/8/8/8/PPPPPPPP/${whiteRank} w KQkq - 0 1`;

  try {
    const gameForFen = new Chess(fen);
    return gameForFen.fen();
  } catch (error) {
    console.warn(
      "Chess960 FEN generation failed, falling back to standard position:",
      error,
    );
    return "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1";
  }
};

export const useChessGame = (gameMode: GameMode) => {
  const gameRef = useRef<Chess | null>(null);
  const [fen, setFen] = useState("start");
  const [pgn, setPgn] = useState("");
  const [lastMove, setLastMove] = useState<{ from: string; to: string } | null>(
    null,
  );
  const [currentMoveNumber, setCurrentMoveNumber] = useState(0);

  const game = useMemo(() => {
    if (typeof window === "undefined") return null;
    if (gameRef.current === null) {
      gameRef.current = new Chess();
    }
    return gameRef.current;
  }, []);

  const updateBoardState = useCallback(
    (theGame?: Chess) => {
      const g = theGame || game;
      if (!g) return;
      setFen(g.fen());
      setPgn(g.pgn({ maxWidth: 5, newline: " " }));
    },
    [game],
  );

  const resetGame = useCallback(() => {
    if (!game) return;
    const isFreestyle = gameMode === "freestyle-ai-vs-ai";
    game.clear();
    if (isFreestyle) {
      const randomFen = generateChess960Fen();
      game.load(randomFen);
      game.header("Variant", "Chess960");
      game.header("FEN", randomFen);
      game.header("SetUp", "1");
    } else {
      game.reset();
    }
    updateBoardState();
    setLastMove(null);
    setCurrentMoveNumber(0);
    return game.fen();
  }, [game, gameMode, updateBoardState]);

  const processMove = useCallback(
    (move: string | { from: Square; to: Square; promotion?: string }) => {
      if (!game) return null;
      const madeMove = game.move(move);
      if (madeMove) {
        updateBoardState();
        setLastMove({ from: madeMove.from, to: madeMove.to });
        setCurrentMoveNumber(game.history().length);
      }
      return madeMove;
    },
    [game, updateBoardState],
  );

  const loadPgn = useCallback(
    (pgnToLoad: string) => {
      if (!game) return;
      game.loadPgn(pgnToLoad);
      updateBoardState();
      setCurrentMoveNumber(game.history().length);
    },
    [game, updateBoardState],
  );

  const navigateToMove = useCallback(
    (moveNumber: number) => {
      if (!game) return;
      const history = game.history({ verbose: true });
      if (moveNumber < 0 || moveNumber > history.length) return;

      const tempGame = new Chess();
      const headers = game.header();
      Object.keys(headers).forEach((key) => {
        const value = headers[key];
        if (value !== null) {
          tempGame.header(key, value);
        }
      });

      const variant = game.header().Variant;
      const fenHeader = game.header().FEN;
      if (variant === "Chess960" && fenHeader) {
        tempGame.load(fenHeader);
      } else if (fenHeader) {
        tempGame.load(fenHeader);
      }

      history
        .slice(0, moveNumber)
        .forEach((move: Move) => tempGame.move(move.san));

      updateBoardState(tempGame);
      setCurrentMoveNumber(moveNumber);
      setLastMove(
        moveNumber > 0
          ? {
              from: history[moveNumber - 1].from,
              to: history[moveNumber - 1].to,
            }
          : null,
      );
    },
    [game, updateBoardState],
  );

  return {
    game,
    fen,
    pgn,
    lastMove,
    currentMoveNumber,
    resetGame,
    processMove,
    loadPgn,
    navigateToMove,
    updateBoardState,
    setCurrentMoveNumber,
  };
};
