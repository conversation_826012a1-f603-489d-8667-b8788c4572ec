import { useState, useCallback } from "react";
import type { GameMode as ChessGameMode } from "./useChessGame";

export type Player = "white" | "black";
export type GameState =
  | "idle"
  | "starting"
  | "running"
  | "paused"
  | "processing-move"
  | "completed"
  | "error";
export type Model =
  | "gemini-2.0-flash"
  | "gemini-2.5-flash"
  | "gemini-2.5-pro"
  | "openrouter/openai/gpt-oss-20b:free"
  | "openrouter/z-ai/glm-4.5-air:free"
  | "openrouter/moonshotai/kimi-k2:free"
  | "openrouter/google/gemma-3n-e2b-it:free"
  | "openrouter/deepseek/deepseek-r1-0528:free"
  | "openrouter/qwen/qwen3-235b-a22b:free";

export const ALL_MODELS: Model[] = [
  "gemini-2.0-flash",
  "gemini-2.5-flash",
  "gemini-2.5-pro",
  "openrouter/openai/gpt-oss-20b:free",
  "openrouter/z-ai/glm-4.5-air:free",
  "openrouter/moonshotai/kimi-k2:free",
  "openrouter/google/gemma-3n-e2b-it:free",
  "openrouter/deepseek/deepseek-r1-0528:free",
  "openrouter/qwen/qwen3-235b-a22b:free",
];

export const useGameState = () => {
  const [gameMode, setGameMode] = useState<ChessGameMode>("ai-vs-ai");
  const [humanPlayer, setHumanPlayer] = useState<Player>("white");
  const [whiteModel, setWhiteModel] = useState<Model>("gemini-2.0-flash");
  const [blackModel, setBlackModel] = useState<Model>("gemini-2.5-pro");
  const [isGameRunning, setIsGameRunning] = useState(false);
  const [isGameStarted, setIsGameStarted] = useState(false);
  const [gameStatus, setGameStatus] = useState("Game has not started.");
  const [currentGameState, setCurrentGameState] = useState<GameState>("idle");
  const [logs, setLogs] = useState<string[]>(["Welcome to Chess Duel Arena!"]);

  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs((prev) => [`[${timestamp}] ${message}`, ...prev]);
  }, []);

  const startGame = useCallback(() => {
    setIsGameStarted(true);
    setIsGameRunning(true);
    setCurrentGameState("starting");
  }, []);

  const pauseGame = useCallback(() => {
    setIsGameRunning(false);
    setCurrentGameState("paused");
    addLog("Game paused.");
  }, [addLog]);

  const resumeGame = useCallback(() => {
    setIsGameRunning(true);
    setCurrentGameState("running");
    addLog("Game resumed.");
  }, [addLog]);

  const endGame = useCallback(
    (status: string) => {
      addLog(`GAME_END: ${status}`);
      setGameStatus(status);
      setIsGameRunning(false);
      setCurrentGameState("completed");
    },
    [addLog],
  );

  const resetGameState = useCallback(() => {
    setIsGameRunning(false);
    setIsGameStarted(false);
    setCurrentGameState("idle");
    setLogs(["Game reset. Select mode, models and start."]);
    setGameStatus("Game reset. Select mode, models and start.");
  }, []);

  return {
    gameMode,
    setGameMode,
    humanPlayer,
    setHumanPlayer,
    whiteModel,
    setWhiteModel,
    blackModel,
    setBlackModel,
    isGameRunning,
    isGameStarted,
    gameStatus,
    setGameStatus,
    currentGameState,
    setCurrentGameState,
    logs,
    addLog,
    startGame,
    pauseGame,
    resumeGame,
    endGame,
    resetGameState,
  };
};
