import { useState, useEffect, useCallback, useRef } from "react";
import { useToast } from "@/hooks/use-toast";

export const useSpectator = (
  gameId: string | null,
  onMessage: (data: any) => void,
  addLog: (message: string) => void,
) => {
  const { toast } = useToast();
  const eventSourceRef = useRef<EventSource | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const reconnectTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(
    null,
  );

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
      setIsConnected(false);
      setIsConnecting(false);
      addLog(`SPECTATE: Disconnected from game ${gameId}.`);
    }
  }, [addLog, gameId]);

  const connect = useCallback(() => {
    if (!gameId || isConnected || isConnecting) return;

    disconnect(); // Ensure any old connection is closed

    setIsConnecting(true);
    addLog(`SPECTATE: Connecting to live game ${gameId}...`);

    const es = new EventSource(`/api/spectate/${gameId}`);
    eventSourceRef.current = es;

    es.onopen = () => {
      addLog(`SPECTATE: SSE connection established for game ${gameId}`);
      setIsConnecting(false);
      setIsConnected(true);
      toast({
        title: "Spectator Connected",
        description: `Now watching game ${gameId}`,
      });
    };

    es.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        addLog(`SPECTATE_ERROR: Failed to parse SSE message - ${error}`);
      }
    };

    es.onerror = () => {
      addLog(`SPECTATE_ERROR: SSE connection error for game ${gameId}.`);
      es.close();
      setIsConnected(false);
      setIsConnecting(false);

      // Simple auto-reconnect logic
      if (reconnectTimeoutRef.current)
        clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = setTimeout(() => {
        addLog(`SPECTATE: Attempting to reconnect to game ${gameId}...`);
        connect();
      }, 5000);
    };
  }, [gameId, isConnected, isConnecting, disconnect, addLog, onMessage, toast]);

  useEffect(() => {
    if (gameId) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [gameId, connect, disconnect]);

  return { isConnected, isConnecting };
};
