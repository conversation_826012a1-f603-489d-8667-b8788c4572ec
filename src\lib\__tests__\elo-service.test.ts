import { describe, it, expect, beforeEach, vi } from "vitest";
import { ELOService, MatchResult } from "../elo-service";
import { ELOCalculator } from "../elo-calculator";
import prisma from "../db";

// Mock Prisma
vi.mock("../db", () => ({
  default: {
    $transaction: vi.fn(),
    lLMProfile: {
      findUnique: vi.fn(),
      update: vi.fn(),
    },
    match: {
      update: vi.fn(),
    },
  },
}));

describe("ELOService", () => {
  let service: ELOService;
  let mockTransaction: any;

  beforeEach(() => {
    service = new ELOService();
    mockTransaction = {
      lLMProfile: {
        findUnique: vi.fn(),
        update: vi.fn(),
      },
      match: {
        update: vi.fn(),
      },
    };

    // Reset all mocks
    vi.clearAllMocks();
  });

  describe("updateEloRatingsInTransaction", () => {
    const mockWhiteProfile = {
      id: "white-id",
      eloRating: 1900,
      gamesPlayed: 10,
      wins: 5,
      losses: 3,
      draws: 2,
    };

    const mockBlackProfile = {
      id: "black-id",
      eloRating: 1800,
      gamesPlayed: 15,
      wins: 8,
      losses: 5,
      draws: 2,
    };

    const matchResult: MatchResult = {
      matchId: "match-id",
      whiteProfileId: "white-id",
      blackProfileId: "black-id",
      result: "white",
      gameId: "game-id",
    };

    beforeEach(() => {
      mockTransaction.lLMProfile.findUnique
        .mockResolvedValueOnce(mockWhiteProfile)
        .mockResolvedValueOnce(mockBlackProfile);

      mockTransaction.lLMProfile.update
        .mockResolvedValueOnce({ ...mockWhiteProfile, eloRating: 1907 })
        .mockResolvedValueOnce({ ...mockBlackProfile, eloRating: 1793 });

      mockTransaction.match.update.mockResolvedValueOnce({});
    });

    it("should successfully update ELO ratings for both players", async () => {
      const result = await service.updateEloRatingsInTransaction(
        mockTransaction,
        matchResult,
      );

      expect(result.whiteProfile.oldElo).toBe(1900);
      expect(result.blackProfile.oldElo).toBe(1800);
      expect(result.whiteProfile.eloChange).toBeGreaterThan(0);
      expect(result.blackProfile.eloChange).toBeLessThan(0);
      expect(result.whiteProfile.newGamesPlayed).toBe(11);
      expect(result.whiteProfile.newWins).toBe(6);
    });

    it("should update game statistics correctly for white win", async () => {
      const result = await service.updateEloRatingsInTransaction(
        mockTransaction,
        matchResult,
      );

      expect(result.whiteProfile.newWins).toBe(mockWhiteProfile.wins + 1);
      expect(result.whiteProfile.newLosses).toBe(mockWhiteProfile.losses);
      expect(result.whiteProfile.newDraws).toBe(mockWhiteProfile.draws);

      expect(result.blackProfile.newWins).toBe(mockBlackProfile.wins);
      expect(result.blackProfile.newLosses).toBe(mockBlackProfile.losses + 1);
      expect(result.blackProfile.newDraws).toBe(mockBlackProfile.draws);
    });

    it("should update game statistics correctly for black win", async () => {
      const blackWinResult = { ...matchResult, result: "black" as const };

      const result = await service.updateEloRatingsInTransaction(
        mockTransaction,
        blackWinResult,
      );

      expect(result.whiteProfile.newLosses).toBe(mockWhiteProfile.losses + 1);
      expect(result.blackProfile.newWins).toBe(mockBlackProfile.wins + 1);
    });

    it("should update game statistics correctly for draw", async () => {
      const drawResult = { ...matchResult, result: "draw" as const };

      const result = await service.updateEloRatingsInTransaction(
        mockTransaction,
        drawResult,
      );

      expect(result.whiteProfile.newDraws).toBe(mockWhiteProfile.draws + 1);
      expect(result.blackProfile.newDraws).toBe(mockBlackProfile.draws + 1);
    });

    it("should update match record with ELO changes", async () => {
      await service.updateEloRatingsInTransaction(mockTransaction, matchResult);

      expect(mockTransaction.match.update).toHaveBeenCalledWith({
        where: { id: "match-id" },
        data: expect.objectContaining({
          whiteEloChange: expect.any(Number),
          blackEloChange: expect.any(Number),
          status: "COMPLETED",
          completedAt: expect.any(Date),
          gameId: "game-id",
        }),
      });
    });

    it("should update both profiles with new ELO and stats", async () => {
      await service.updateEloRatingsInTransaction(mockTransaction, matchResult);

      expect(mockTransaction.lLMProfile.update).toHaveBeenCalledTimes(2);

      // Check white profile update
      expect(mockTransaction.lLMProfile.update).toHaveBeenCalledWith({
        where: { id: "white-id" },
        data: expect.objectContaining({
          eloRating: expect.any(Number),
          gamesPlayed: 11,
          wins: 6,
          losses: 3,
          draws: 2,
          lastMatchAt: expect.any(Date),
        }),
      });

      // Check black profile update
      expect(mockTransaction.lLMProfile.update).toHaveBeenCalledWith({
        where: { id: "black-id" },
        data: expect.objectContaining({
          eloRating: expect.any(Number),
          gamesPlayed: 16,
          wins: 8,
          losses: 6,
          draws: 2,
          lastMatchAt: expect.any(Date),
        }),
      });
    });

    it("should throw error if white profile not found", async () => {
      // Reset the mock and set up new behavior
      mockTransaction.lLMProfile.findUnique.mockReset();
      mockTransaction.lLMProfile.findUnique
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce(mockBlackProfile);

      await expect(
        service.updateEloRatingsInTransaction(mockTransaction, matchResult),
      ).rejects.toThrow("White player profile not found: white-id");
    });

    it("should throw error if black profile not found", async () => {
      // Reset the mock and set up new behavior
      mockTransaction.lLMProfile.findUnique.mockReset();
      mockTransaction.lLMProfile.findUnique
        .mockResolvedValueOnce(mockWhiteProfile)
        .mockResolvedValueOnce(null);

      await expect(
        service.updateEloRatingsInTransaction(mockTransaction, matchResult),
      ).rejects.toThrow("Black player profile not found: black-id");
    });
  });

  describe("updateEloRatings", () => {
    it("should wrap transaction call", async () => {
      const matchResult: MatchResult = {
        matchId: "match-id",
        whiteProfileId: "white-id",
        blackProfileId: "black-id",
        result: "white",
      };

      const mockResult = {
        whiteProfile: {
          id: "white-id",
          oldElo: 1900,
          newElo: 1910,
          eloChange: 10,
        },
        blackProfile: {
          id: "black-id",
          oldElo: 1800,
          newElo: 1790,
          eloChange: -10,
        },
      };

      (prisma.$transaction as any).mockImplementation(async (callback: any) => {
        return callback(mockTransaction);
      });

      // Mock the transaction method
      vi.spyOn(service, "updateEloRatingsInTransaction").mockResolvedValue(
        mockResult as any,
      );

      const result = await service.updateEloRatings(matchResult);

      expect(prisma.$transaction).toHaveBeenCalled();
      expect(result).toEqual(mockResult);
    });
  });

  describe("batchUpdateEloRatings", () => {
    it("should process multiple matches in single transaction", async () => {
      const matchResults: MatchResult[] = [
        {
          matchId: "match-1",
          whiteProfileId: "white-1",
          blackProfileId: "black-1",
          result: "white",
        },
        {
          matchId: "match-2",
          whiteProfileId: "white-2",
          blackProfileId: "black-2",
          result: "black",
        },
      ];

      const mockResults = [
        { whiteProfile: { id: "white-1" }, blackProfile: { id: "black-1" } },
        { whiteProfile: { id: "white-2" }, blackProfile: { id: "black-2" } },
      ];

      (prisma.$transaction as any).mockImplementation(async (callback: any) => {
        return callback(mockTransaction);
      });

      vi.spyOn(service, "updateEloRatingsInTransaction")
        .mockResolvedValueOnce(mockResults[0] as any)
        .mockResolvedValueOnce(mockResults[1] as any);

      const results = await service.batchUpdateEloRatings(matchResults);

      expect(results).toHaveLength(2);
      expect(service.updateEloRatingsInTransaction).toHaveBeenCalledTimes(2);
    });
  });

  describe("validation", () => {
    it("should validate minimum ELO rating", async () => {
      const lowEloProfile = {
        id: "low-elo",
        eloRating: 50, // Below minimum of 100
        gamesPlayed: 5,
        wins: 0,
        losses: 5,
        draws: 0,
      };

      mockTransaction.lLMProfile.findUnique
        .mockResolvedValueOnce(lowEloProfile)
        .mockResolvedValueOnce({
          id: "normal",
          eloRating: 1800,
          gamesPlayed: 10,
          wins: 5,
          losses: 3,
          draws: 2,
        });

      const matchResult: MatchResult = {
        matchId: "match-id",
        whiteProfileId: "low-elo",
        blackProfileId: "normal",
        result: "white",
      };

      await expect(
        service.updateEloRatingsInTransaction(mockTransaction, matchResult),
      ).rejects.toThrow(
        "ELO rating below minimum (100) for profile low-elo: 50",
      );
    });

    it("should validate maximum ELO rating", async () => {
      const highEloProfile = {
        id: "high-elo",
        eloRating: 5000, // Above maximum of 4000
        gamesPlayed: 100,
        wins: 90,
        losses: 5,
        draws: 5,
      };

      mockTransaction.lLMProfile.findUnique
        .mockResolvedValueOnce(highEloProfile)
        .mockResolvedValueOnce({
          id: "normal",
          eloRating: 1800,
          gamesPlayed: 10,
          wins: 5,
          losses: 3,
          draws: 2,
        });

      const matchResult: MatchResult = {
        matchId: "match-id",
        whiteProfileId: "high-elo",
        blackProfileId: "normal",
        result: "white",
      };

      await expect(
        service.updateEloRatingsInTransaction(mockTransaction, matchResult),
      ).rejects.toThrow(
        "ELO rating above maximum (4000) for profile high-elo: 5000",
      );
    });
  });

  describe("getEloStatistics", () => {
    it("should return comprehensive profile statistics", async () => {
      const mockProfile = {
        id: "profile-id",
        name: "Test Profile",
        model: "test-model",
        eloRating: 1950,
        gamesPlayed: 20,
        wins: 12,
        losses: 6,
        draws: 2,
        isActive: true,
        lastMatchAt: new Date(),
        whiteMatches: [
          {
            completedAt: new Date(),
            blackProfile: { name: "Opponent 1", eloRating: 1900 },
            game: { result: "1-0" },
          },
        ],
        blackMatches: [
          {
            completedAt: new Date(),
            whiteProfile: { name: "Opponent 2", eloRating: 2000 },
            game: { result: "0-1" },
          },
        ],
      };

      (prisma.lLMProfile.findUnique as any).mockResolvedValue(mockProfile);

      const result = await service.getEloStatistics("profile-id");

      expect(result.profile.id).toBe("profile-id");
      expect(result.statistics.winRate).toBe(60); // 12/20 * 100
      expect(result.statistics.averageOpponentElo).toBe(1950); // (1900 + 2000) / 2
      expect(result.statistics.volatilityStatus).toBe("stable"); // 20 games
      expect(result.statistics.kFactor).toBe(20); // Stable K-factor
      expect(result.recentMatches).toHaveLength(2);
    });

    it("should handle profile with no matches", async () => {
      const mockProfile = {
        id: "new-profile",
        name: "New Profile",
        model: "test-model",
        eloRating: 1900,
        gamesPlayed: 0,
        wins: 0,
        losses: 0,
        draws: 0,
        isActive: true,
        lastMatchAt: null,
        whiteMatches: [],
        blackMatches: [],
      };

      (prisma.lLMProfile.findUnique as any).mockResolvedValue(mockProfile);

      const result = await service.getEloStatistics("new-profile");

      expect(result.statistics.winRate).toBe(0);
      expect(result.statistics.averageOpponentElo).toBe(0);
      expect(result.statistics.volatilityStatus).toBe("volatile");
      expect(result.statistics.kFactor).toBe(100);
      expect(result.recentMatches).toHaveLength(0);
    });

    it("should throw error if profile not found", async () => {
      (prisma.lLMProfile.findUnique as any).mockResolvedValue(null);

      await expect(service.getEloStatistics("nonexistent")).rejects.toThrow(
        "Profile not found: nonexistent",
      );
    });
  });

  describe("custom calculator configuration", () => {
    it("should use custom ELO calculator", () => {
      const customCalculator = new ELOCalculator({
        volatileKFactor: 80,
        stabilizingKFactor: 40,
        stableKFactor: 15,
      });

      const customService = new ELOService(customCalculator);

      // Access the private calculator through a test method
      expect((customService as any).calculator.getKFactor(0)).toBe(80);
      expect((customService as any).calculator.getKFactor(7)).toBe(40);
      expect((customService as any).calculator.getKFactor(15)).toBe(15);
    });
  });
});
