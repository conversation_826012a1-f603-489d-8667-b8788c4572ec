import { describe, it, expect, beforeEach, vi, afterEach } from "vitest";

// Mock Prisma first
vi.mock("@/lib/db", () => ({
  default: {
    lLMProfile: {
      create: vi.fn(),
      findMany: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    match: {
      count: vi.fn(),
      findMany: vi.fn(),
    },
  },
}));

import {
  createProfile,
  getAllProfiles,
  getActiveProfiles,
  getProfileById,
  updateProfile,
  deleteProfile,
  getProfileWithRecentMatches,
  calculateProfileStatistics,
  getProfileVolatilityStatus,
  updateProfileAfterMatch,
} from "../profile-service";
import prisma from "@/lib/db";

const mockPrisma = prisma as any;

describe("Profile Service", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe("createProfile", () => {
    it("should create a profile with default ELO rating", async () => {
      const mockProfile = {
        id: "profile-1",
        name: "GPT-4",
        model: "openai/gpt-4",
        eloRating: 1900,
        gamesPlayed: 0,
        wins: 0,
        losses: 0,
        draws: 0,
        isActive: true,
        lastMatchAt: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.lLMProfile.create.mockResolvedValue(mockProfile);

      const result = await createProfile({
        name: "GPT-4",
        model: "openai/gpt-4",
      });

      expect(mockPrisma.lLMProfile.create).toHaveBeenCalledWith({
        data: {
          name: "GPT-4",
          model: "openai/gpt-4",
          eloRating: 1900,
        },
      });
      expect(result).toEqual(mockProfile);
    });
  });

  describe("getAllProfiles", () => {
    it("should return all profiles ordered by ELO rating", async () => {
      const mockProfiles = [
        { id: "1", name: "Profile 1", eloRating: 2000 },
        { id: "2", name: "Profile 2", eloRating: 1800 },
      ];

      mockPrisma.lLMProfile.findMany.mockResolvedValue(mockProfiles);

      const result = await getAllProfiles();

      expect(mockPrisma.lLMProfile.findMany).toHaveBeenCalledWith({
        orderBy: [{ eloRating: "desc" }, { name: "asc" }],
      });
      expect(result).toEqual(mockProfiles);
    });
  });

  describe("getActiveProfiles", () => {
    it("should return only active profiles", async () => {
      const mockProfiles = [{ id: "1", name: "Profile 1", isActive: true }];

      mockPrisma.lLMProfile.findMany.mockResolvedValue(mockProfiles);

      const result = await getActiveProfiles();

      expect(mockPrisma.lLMProfile.findMany).toHaveBeenCalledWith({
        where: { isActive: true },
        orderBy: [{ eloRating: "desc" }, { name: "asc" }],
      });
      expect(result).toEqual(mockProfiles);
    });
  });

  describe("getProfileById", () => {
    it("should return profile by ID", async () => {
      const mockProfile = { id: "profile-1", name: "Test Profile" };
      mockPrisma.lLMProfile.findUnique.mockResolvedValue(mockProfile);

      const result = await getProfileById("profile-1");

      expect(mockPrisma.lLMProfile.findUnique).toHaveBeenCalledWith({
        where: { id: "profile-1" },
      });
      expect(result).toEqual(mockProfile);
    });

    it("should return null if profile not found", async () => {
      mockPrisma.lLMProfile.findUnique.mockResolvedValue(null);

      const result = await getProfileById("nonexistent");

      expect(result).toBeNull();
    });
  });

  describe("updateProfile", () => {
    it("should update profile with new data", async () => {
      const mockUpdatedProfile = {
        id: "profile-1",
        name: "Updated Name",
        model: "updated-model",
        isActive: false,
      };

      mockPrisma.lLMProfile.update.mockResolvedValue(mockUpdatedProfile);

      const result = await updateProfile("profile-1", {
        name: "Updated Name",
        model: "updated-model",
        isActive: false,
      });

      expect(mockPrisma.lLMProfile.update).toHaveBeenCalledWith({
        where: { id: "profile-1" },
        data: {
          name: "Updated Name",
          model: "updated-model",
          isActive: false,
          updatedAt: expect.any(Date),
        },
      });
      expect(result).toEqual(mockUpdatedProfile);
    });
  });

  describe("deleteProfile", () => {
    it("should delete profile if no matches exist", async () => {
      mockPrisma.match.count.mockResolvedValue(0);
      mockPrisma.lLMProfile.delete.mockResolvedValue({});

      await deleteProfile("profile-1");

      expect(mockPrisma.match.count).toHaveBeenCalledWith({
        where: {
          OR: [
            { whiteProfileId: "profile-1" },
            { blackProfileId: "profile-1" },
          ],
        },
      });
      expect(mockPrisma.lLMProfile.delete).toHaveBeenCalledWith({
        where: { id: "profile-1" },
      });
    });

    it("should throw error if profile has matches", async () => {
      mockPrisma.match.count.mockResolvedValue(5);

      await expect(deleteProfile("profile-1")).rejects.toThrow(
        "Cannot delete profile with existing matches. Deactivate instead.",
      );

      expect(mockPrisma.lLMProfile.delete).not.toHaveBeenCalled();
    });
  });

  describe("calculateProfileStatistics", () => {
    it("should calculate win rate and average opponent ELO", async () => {
      const mockMatches = [
        {
          whiteProfileId: "profile-1",
          blackProfileId: "profile-2",
          whiteProfile: { eloRating: 1900 },
          blackProfile: { eloRating: 1800 },
          game: { result: "1-0" }, // White wins
        },
        {
          whiteProfileId: "profile-2",
          blackProfileId: "profile-1",
          whiteProfile: { eloRating: 1800 },
          blackProfile: { eloRating: 1900 },
          game: { result: "0-1" }, // Black (profile-1) wins
        },
        {
          whiteProfileId: "profile-1",
          blackProfileId: "profile-3",
          whiteProfile: { eloRating: 1900 },
          blackProfile: { eloRating: 2000 },
          game: { result: "1/2-1/2" }, // Draw
        },
      ];

      mockPrisma.match.findMany.mockResolvedValue(mockMatches);

      const result = await calculateProfileStatistics("profile-1");

      // 2 wins + 0.5 for draw = 2.5 out of 3 = 83.33%
      expect(result.winRate).toBeCloseTo(83.33, 1);
      // Average opponent ELO: (1800 + 1800 + 2000) / 3 = 1866.67
      expect(result.averageOpponentElo).toBe(1867);
    });

    it("should return zero stats for profile with no matches", async () => {
      mockPrisma.match.findMany.mockResolvedValue([]);

      const result = await calculateProfileStatistics("profile-1");

      expect(result.winRate).toBe(0);
      expect(result.averageOpponentElo).toBe(0);
    });
  });

  describe("getProfileVolatilityStatus", () => {
    it("should return volatile for less than 5 games", () => {
      expect(getProfileVolatilityStatus(0)).toBe("volatile");
      expect(getProfileVolatilityStatus(4)).toBe("volatile");
    });

    it("should return stabilizing for 5-9 games", () => {
      expect(getProfileVolatilityStatus(5)).toBe("stabilizing");
      expect(getProfileVolatilityStatus(9)).toBe("stabilizing");
    });

    it("should return stable for 10+ games", () => {
      expect(getProfileVolatilityStatus(10)).toBe("stable");
      expect(getProfileVolatilityStatus(100)).toBe("stable");
    });
  });

  describe("updateProfileAfterMatch", () => {
    it("should update profile stats after a win", async () => {
      const mockProfile = {
        id: "profile-1",
        gamesPlayed: 5,
        wins: 3,
        losses: 1,
        draws: 1,
        eloRating: 1950,
      };

      mockPrisma.lLMProfile.findUnique.mockResolvedValue(mockProfile);
      mockPrisma.lLMProfile.update.mockResolvedValue({});

      await updateProfileAfterMatch("profile-1", "win", 25);

      expect(mockPrisma.lLMProfile.update).toHaveBeenCalledWith({
        where: { id: "profile-1" },
        data: {
          gamesPlayed: 6,
          wins: 4,
          eloRating: 1975,
          lastMatchAt: expect.any(Date),
        },
      });
    });

    it("should update profile stats after a loss", async () => {
      const mockProfile = {
        id: "profile-1",
        gamesPlayed: 5,
        wins: 3,
        losses: 1,
        draws: 1,
        eloRating: 1950,
      };

      mockPrisma.lLMProfile.findUnique.mockResolvedValue(mockProfile);
      mockPrisma.lLMProfile.update.mockResolvedValue({});

      await updateProfileAfterMatch("profile-1", "loss", -20);

      expect(mockPrisma.lLMProfile.update).toHaveBeenCalledWith({
        where: { id: "profile-1" },
        data: {
          gamesPlayed: 6,
          losses: 2,
          eloRating: 1930,
          lastMatchAt: expect.any(Date),
        },
      });
    });

    it("should update profile stats after a draw", async () => {
      const mockProfile = {
        id: "profile-1",
        gamesPlayed: 5,
        wins: 3,
        losses: 1,
        draws: 1,
        eloRating: 1950,
      };

      mockPrisma.lLMProfile.findUnique.mockResolvedValue(mockProfile);
      mockPrisma.lLMProfile.update.mockResolvedValue({});

      await updateProfileAfterMatch("profile-1", "draw", 5);

      expect(mockPrisma.lLMProfile.update).toHaveBeenCalledWith({
        where: { id: "profile-1" },
        data: {
          gamesPlayed: 6,
          draws: 2,
          eloRating: 1955,
          lastMatchAt: expect.any(Date),
        },
      });
    });

    it("should throw error if profile not found", async () => {
      mockPrisma.lLMProfile.findUnique.mockResolvedValue(null);

      await expect(
        updateProfileAfterMatch("nonexistent", "win", 25),
      ).rejects.toThrow("Profile not found");
    });
  });
});
