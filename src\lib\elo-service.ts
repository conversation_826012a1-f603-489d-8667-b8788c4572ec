/**
 * ELO Service Layer
 *
 * Handles ELO rating updates with database transactions and validation.
 * Provides atomic operations for updating player ratings after matches.
 */

import prisma from "./db";
import {
  ELOCalculator,
  GameResult,
  ELOCalculationResult,
} from "./elo-calculator";
import { PrismaClient } from "@prisma/client";

type PrismaTransactionClient = Omit<
  PrismaClient,
  "$connect" | "$disconnect" | "$on" | "$transaction" | "$use" | "$extends"
>;

export interface MatchResult {
  matchId: string;
  whiteProfileId: string;
  blackProfileId: string;
  result: GameResult;
  gameId?: string;
}

export interface ELOUpdateResult {
  whiteProfile: {
    id: string;
    oldElo: number;
    newElo: number;
    eloChange: number;
    newGamesPlayed: number;
    newWins: number;
    newLosses: number;
    newDraws: number;
  };
  blackProfile: {
    id: string;
    oldElo: number;
    newElo: number;
    eloChange: number;
    newGamesPlayed: number;
    newWins: number;
    newLosses: number;
    newDraws: number;
  };
  calculationResult: ELOCalculationResult;
}

export class ELOService {
  private calculator: ELOCalculator;

  constructor(calculator?: ELOCalculator) {
    this.calculator = calculator || new ELOCalculator();
  }

  /**
   * Update ELO ratings for both players after a match
   * Uses database transaction to ensure atomicity
   */
  async updateEloRatings(matchResult: MatchResult): Promise<ELOUpdateResult> {
    return await prisma.$transaction(async (tx) => {
      return await this.updateEloRatingsInTransaction(tx, matchResult);
    });
  }

  /**
   * Update ELO ratings within an existing transaction
   * Useful for batch operations or when part of larger transaction
   */
  async updateEloRatingsInTransaction(
    tx: PrismaTransactionClient,
    matchResult: MatchResult,
  ): Promise<ELOUpdateResult> {
    const { matchId, whiteProfileId, blackProfileId, result, gameId } =
      matchResult;

    // Fetch both profiles with current stats
    const [whiteProfile, blackProfile] = await Promise.all([
      tx.lLMProfile.findUnique({
        where: { id: whiteProfileId },
        select: {
          id: true,
          eloRating: true,
          gamesPlayed: true,
          wins: true,
          losses: true,
          draws: true,
        },
      }),
      tx.lLMProfile.findUnique({
        where: { id: blackProfileId },
        select: {
          id: true,
          eloRating: true,
          gamesPlayed: true,
          wins: true,
          losses: true,
          draws: true,
        },
      }),
    ]);

    if (!whiteProfile) {
      throw new Error(`White player profile not found: ${whiteProfileId}`);
    }
    if (!blackProfile) {
      throw new Error(`Black player profile not found: ${blackProfileId}`);
    }

    // Validate ELO ratings
    this.validateEloRating(whiteProfile.eloRating, whiteProfileId);
    this.validateEloRating(blackProfile.eloRating, blackProfileId);

    // Calculate ELO changes
    const calculationResult = this.calculator.calculateEloChange(
      whiteProfile.eloRating,
      blackProfile.eloRating,
      whiteProfile.gamesPlayed,
      blackProfile.gamesPlayed,
      result,
    );

    // Validate maximum single-game changes
    this.validateEloChange(calculationResult.whiteEloChange, whiteProfileId);
    this.validateEloChange(calculationResult.blackEloChange, blackProfileId);

    // Calculate new game statistics
    const whiteStats = this.calculateNewStats(whiteProfile, result, "white");
    const blackStats = this.calculateNewStats(blackProfile, result, "black");

    // Update both profiles atomically
    const [updatedWhiteProfile, updatedBlackProfile] = await Promise.all([
      tx.lLMProfile.update({
        where: { id: whiteProfileId },
        data: {
          eloRating: calculationResult.whiteNewElo,
          gamesPlayed: whiteStats.gamesPlayed,
          wins: whiteStats.wins,
          losses: whiteStats.losses,
          draws: whiteStats.draws,
          lastMatchAt: new Date(),
        },
      }),
      tx.lLMProfile.update({
        where: { id: blackProfileId },
        data: {
          eloRating: calculationResult.blackNewElo,
          gamesPlayed: blackStats.gamesPlayed,
          wins: blackStats.wins,
          losses: blackStats.losses,
          draws: blackStats.draws,
          lastMatchAt: new Date(),
        },
      }),
    ]);

    // Update match record with ELO changes (only if matchId is provided)
    if (matchId) {
      await tx.match.update({
        where: { id: matchId },
        data: {
          whiteEloChange: calculationResult.whiteEloChange,
          blackEloChange: calculationResult.blackEloChange,
          status: "COMPLETED",
          completedAt: new Date(),
          ...(gameId && { gameId }),
        },
      });
    }

    return {
      whiteProfile: {
        id: whiteProfile.id,
        oldElo: whiteProfile.eloRating,
        newElo: calculationResult.whiteNewElo,
        eloChange: calculationResult.whiteEloChange,
        newGamesPlayed: whiteStats.gamesPlayed,
        newWins: whiteStats.wins,
        newLosses: whiteStats.losses,
        newDraws: whiteStats.draws,
      },
      blackProfile: {
        id: blackProfile.id,
        oldElo: blackProfile.eloRating,
        newElo: calculationResult.blackNewElo,
        eloChange: calculationResult.blackEloChange,
        newGamesPlayed: blackStats.gamesPlayed,
        newWins: blackStats.wins,
        newLosses: blackStats.losses,
        newDraws: blackStats.draws,
      },
      calculationResult,
    };
  }

  /**
   * Batch update ELO ratings for multiple matches
   * Useful for tournament completion or bulk processing
   */
  async batchUpdateEloRatings(
    matchResults: MatchResult[],
  ): Promise<ELOUpdateResult[]> {
    return await prisma.$transaction(async (tx) => {
      const results: ELOUpdateResult[] = [];

      for (const matchResult of matchResults) {
        const result = await this.updateEloRatingsInTransaction(
          tx,
          matchResult,
        );
        results.push(result);
      }

      return results;
    });
  }

  /**
   * Get ELO statistics for a profile
   */
  async getEloStatistics(profileId: string) {
    const profile = await prisma.lLMProfile.findUnique({
      where: { id: profileId },
      include: {
        whiteMatches: {
          where: { status: "COMPLETED" },
          orderBy: { completedAt: "desc" },
          take: 10,
          include: {
            blackProfile: { select: { name: true, eloRating: true } },
            game: { select: { result: true } },
          },
        },
        blackMatches: {
          where: { status: "COMPLETED" },
          orderBy: { completedAt: "desc" },
          take: 10,
          include: {
            whiteProfile: { select: { name: true, eloRating: true } },
            game: { select: { result: true } },
          },
        },
      },
    });

    if (!profile) {
      throw new Error(`Profile not found: ${profileId}`);
    }

    // Calculate additional statistics
    const allMatches = [...profile.whiteMatches, ...profile.blackMatches].sort(
      (a, b) =>
        (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0),
    );

    const winRate =
      profile.gamesPlayed > 0 ? (profile.wins / profile.gamesPlayed) * 100 : 0;

    const averageOpponentElo =
      allMatches.length > 0
        ? allMatches.reduce((sum, match) => {
            const opponentElo =
              "whiteProfile" in match
                ? match.whiteProfile.eloRating
                : match.blackProfile.eloRating;
            return sum + opponentElo;
          }, 0) / allMatches.length
        : 0;

    return {
      profile: {
        id: profile.id,
        name: profile.name,
        model: profile.model,
        eloRating: profile.eloRating,
        gamesPlayed: profile.gamesPlayed,
        wins: profile.wins,
        losses: profile.losses,
        draws: profile.draws,
        isActive: profile.isActive,
        lastMatchAt: profile.lastMatchAt,
      },
      statistics: {
        winRate: Math.round(winRate * 100) / 100,
        averageOpponentElo: Math.round(averageOpponentElo),
        volatilityStatus: this.getVolatilityStatus(profile.gamesPlayed),
        kFactor: this.calculator.getKFactor(profile.gamesPlayed),
      },
      recentMatches: allMatches.slice(0, 10),
    };
  }

  /**
   * Validate ELO rating is within acceptable bounds
   */
  private validateEloRating(elo: number, profileId: string): void {
    const config = this.calculator.getConfig();

    if (elo < config.minimumElo) {
      throw new Error(
        `ELO rating below minimum (${config.minimumElo}) for profile ${profileId}: ${elo}`,
      );
    }

    if (elo > 4000) {
      // Reasonable upper bound
      throw new Error(
        `ELO rating above maximum (4000) for profile ${profileId}: ${elo}`,
      );
    }
  }

  /**
   * Validate ELO change is within acceptable bounds
   */
  private validateEloChange(eloChange: number, profileId: string): void {
    const config = this.calculator.getConfig();

    if (Math.abs(eloChange) > config.maximumChange) {
      throw new Error(
        `ELO change exceeds maximum (${config.maximumChange}) for profile ${profileId}: ${eloChange}`,
      );
    }
  }

  /**
   * Calculate new game statistics based on result
   */
  private calculateNewStats(
    profile: {
      gamesPlayed: number;
      wins: number;
      losses: number;
      draws: number;
    },
    result: GameResult,
    playerColor: "white" | "black",
  ) {
    const stats = {
      gamesPlayed: profile.gamesPlayed + 1,
      wins: profile.wins,
      losses: profile.losses,
      draws: profile.draws,
    };

    if (result === "draw") {
      stats.draws += 1;
    } else if (result === playerColor) {
      stats.wins += 1;
    } else {
      stats.losses += 1;
    }

    return stats;
  }

  /**
   * Get volatility status based on games played
   */
  private getVolatilityStatus(
    gamesPlayed: number,
  ): "volatile" | "stabilizing" | "stable" {
    const config = this.calculator.getConfig();

    if (gamesPlayed < config.volatileGames) {
      return "volatile";
    } else if (gamesPlayed < config.stabilizingGames) {
      return "stabilizing";
    } else {
      return "stable";
    }
  }
}

// Export a default instance
export const defaultELOService = new ELOService();
