// Client-side profile service that makes API calls instead of direct database access

export interface LLMProfile {
  id: string;
  name: string;
  model: string;
  eloRating: number;
  gamesPlayed: number;
  wins: number;
  losses: number;
  draws: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProfileInput {
  name: string;
  model: string;
  eloRating?: number;
}

export interface UpdateProfileInput {
  name?: string;
  model?: string;
  eloRating?: number;
  isActive?: boolean;
}

export class ProfileClientService {
  async getAllProfiles(): Promise<LLMProfile[]> {
    try {
      const response = await fetch("/api/profiles");
      if (!response.ok) {
        throw new Error(`Failed to fetch profiles: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error("Error fetching profiles:", error);
      throw error;
    }
  }

  async getProfileById(id: string): Promise<LLMProfile | null> {
    try {
      const response = await fetch(`/api/profiles/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to fetch profile: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error("Error fetching profile:", error);
      throw error;
    }
  }

  async createProfile(input: CreateProfileInput): Promise<LLMProfile> {
    try {
      const response = await fetch("/api/profiles", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Failed to create profile: ${response.status}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error creating profile:", error);
      throw error;
    }
  }

  async updateProfile(
    id: string,
    input: UpdateProfileInput,
  ): Promise<LLMProfile> {
    try {
      const response = await fetch(`/api/profiles/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Failed to update profile: ${response.status}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error updating profile:", error);
      throw error;
    }
  }

  async deleteProfile(id: string): Promise<void> {
    try {
      const response = await fetch(`/api/profiles/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Failed to delete profile: ${response.status}`,
        );
      }
    } catch (error) {
      console.error("Error deleting profile:", error);
      throw error;
    }
  }

  async getProfileStats(id: string): Promise<any> {
    try {
      const response = await fetch(`/api/profiles/${id}/stats`);
      if (!response.ok) {
        throw new Error(`Failed to fetch profile stats: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error("Error fetching profile stats:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const profileService = new ProfileClientService();
