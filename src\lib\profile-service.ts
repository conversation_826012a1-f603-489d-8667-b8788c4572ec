import prisma from "@/lib/db";
import { LLMProfile, Match, Game } from "@prisma/client";

export interface ProfileWithStats extends LLMProfile {
  winRate: number;
  averageOpponentElo: number;
  recentMatches: RecentMatch[];
}

export interface RecentMatch {
  id: string;
  opponent: {
    name: string;
    model: string;
    eloRating: number;
  };
  result: "win" | "loss" | "draw";
  eloChange: number;
  playedAs: "white" | "black";
  completedAt: Date;
  tournament?: {
    name: string;
    round: string;
  };
}

export interface CreateProfileInput {
  name: string;
  model: string;
}

export interface UpdateProfileInput {
  name?: string;
  model?: string;
  isActive?: boolean;
}

/**
 * Create a new LLM profile with default ELO rating
 */
export async function createProfile(
  input: CreateProfileInput,
): Promise<LLMProfile> {
  return await prisma.lLMProfile.create({
    data: {
      name: input.name,
      model: input.model,
      eloRating: 1900, // Default starting ELO
    },
  });
}

/**
 * Get all LLM profiles
 */
export async function getAllProfiles(): Promise<LLMProfile[]> {
  return await prisma.lLMProfile.findMany({
    orderBy: [{ eloRating: "desc" }, { name: "asc" }],
  });
}

/**
 * Get active LLM profiles only
 */
export async function getActiveProfiles(): Promise<LLMProfile[]> {
  return await prisma.lLMProfile.findMany({
    where: { isActive: true },
    orderBy: [{ eloRating: "desc" }, { name: "asc" }],
  });
}

/**
 * Get a single profile by ID
 */
export async function getProfileById(id: string): Promise<LLMProfile | null> {
  return await prisma.lLMProfile.findUnique({
    where: { id },
  });
}

/**
 * Update a profile
 */
export async function updateProfile(
  id: string,
  input: UpdateProfileInput,
): Promise<LLMProfile> {
  return await prisma.lLMProfile.update({
    where: { id },
    data: {
      ...input,
      updatedAt: new Date(),
    },
  });
}

/**
 * Delete a profile (only if no matches exist)
 */
export async function deleteProfile(id: string): Promise<void> {
  // Check if profile has any matches
  const matchCount = await prisma.match.count({
    where: {
      OR: [{ whiteProfileId: id }, { blackProfileId: id }],
    },
  });

  if (matchCount > 0) {
    throw new Error(
      "Cannot delete profile with existing matches. Deactivate instead.",
    );
  }

  await prisma.lLMProfile.delete({
    where: { id },
  });
}

/**
 * Get profile with recent matches and calculated statistics
 */
export async function getProfileWithRecentMatches(
  id: string,
  limit: number = 10,
): Promise<ProfileWithStats | null> {
  const profile = await prisma.lLMProfile.findUnique({
    where: { id },
    include: {
      whiteMatches: {
        where: { status: "COMPLETED" },
        include: {
          blackProfile: true,
          game: true,
          tournament: true,
        },
        orderBy: { completedAt: "desc" },
        take: limit,
      },
      blackMatches: {
        where: { status: "COMPLETED" },
        include: {
          whiteProfile: true,
          game: true,
          tournament: true,
        },
        orderBy: { completedAt: "desc" },
        take: limit,
      },
    },
  });

  if (!profile) return null;

  // Combine and sort all matches
  const allMatches = [
    ...profile.whiteMatches.map((match) => ({
      ...match,
      playedAs: "white" as const,
      opponent: match.blackProfile,
      eloChange: match.whiteEloChange || 0,
    })),
    ...profile.blackMatches.map((match) => ({
      ...match,
      playedAs: "black" as const,
      opponent: match.whiteProfile,
      eloChange: match.blackEloChange || 0,
    })),
  ]
    .sort((a, b) => {
      const aDate = a.completedAt || new Date(0);
      const bDate = b.completedAt || new Date(0);
      return bDate.getTime() - aDate.getTime();
    })
    .slice(0, limit);

  // Calculate statistics
  const stats = await calculateProfileStatistics(id);

  // Format recent matches
  const recentMatches: RecentMatch[] = allMatches.map((match) => {
    let result: "win" | "loss" | "draw" = "draw";

    if (match.game?.result) {
      if (match.game.result === "1-0") {
        result = match.playedAs === "white" ? "win" : "loss";
      } else if (match.game.result === "0-1") {
        result = match.playedAs === "black" ? "win" : "loss";
      }
    }

    return {
      id: match.id,
      opponent: {
        name: match.opponent.name,
        model: match.opponent.model,
        eloRating: match.opponent.eloRating,
      },
      result,
      eloChange: match.eloChange,
      playedAs: match.playedAs,
      completedAt: match.completedAt || new Date(),
      tournament: match.tournament
        ? {
            name: match.tournament.name,
            round: match.round || "",
          }
        : undefined,
    };
  });

  return {
    ...profile,
    winRate: stats.winRate,
    averageOpponentElo: stats.averageOpponentElo,
    recentMatches,
  };
}

/**
 * Calculate profile statistics
 */
export async function calculateProfileStatistics(profileId: string): Promise<{
  winRate: number;
  averageOpponentElo: number;
}> {
  // Get all completed matches for this profile
  const matches = await prisma.match.findMany({
    where: {
      OR: [{ whiteProfileId: profileId }, { blackProfileId: profileId }],
      status: "COMPLETED",
    },
    include: {
      whiteProfile: true,
      blackProfile: true,
      game: true,
    },
  });

  if (matches.length === 0) {
    return {
      winRate: 0,
      averageOpponentElo: 0,
    };
  }

  let wins = 0;
  let totalOpponentElo = 0;

  matches.forEach((match) => {
    const isWhite = match.whiteProfileId === profileId;
    const opponent = isWhite ? match.blackProfile : match.whiteProfile;

    totalOpponentElo += opponent.eloRating;

    if (match.game?.result) {
      if (match.game.result === "1-0" && isWhite) wins++;
      else if (match.game.result === "0-1" && !isWhite) wins++;
      else if (match.game.result === "1/2-1/2") wins += 0.5; // Count draws as half wins
    }
  });

  return {
    winRate: matches.length > 0 ? (wins / matches.length) * 100 : 0,
    averageOpponentElo: Math.round(totalOpponentElo / matches.length),
  };
}

/**
 * Get profile volatility status based on games played
 */
export function getProfileVolatilityStatus(
  gamesPlayed: number,
): "volatile" | "stabilizing" | "stable" {
  if (gamesPlayed < 5) return "volatile";
  if (gamesPlayed < 10) return "stabilizing";
  return "stable";
}

/**
 * Update profile statistics after a match
 */
export async function updateProfileAfterMatch(
  profileId: string,
  result: "win" | "loss" | "draw",
  eloChange: number,
): Promise<void> {
  const profile = await prisma.lLMProfile.findUnique({
    where: { id: profileId },
  });

  if (!profile) {
    throw new Error("Profile not found");
  }

  const updates: any = {
    gamesPlayed: profile.gamesPlayed + 1,
    eloRating: profile.eloRating + eloChange,
    lastMatchAt: new Date(),
  };

  if (result === "win") {
    updates.wins = profile.wins + 1;
  } else if (result === "loss") {
    updates.losses = profile.losses + 1;
  } else {
    updates.draws = profile.draws + 1;
  }

  await prisma.lLMProfile.update({
    where: { id: profileId },
    data: updates,
  });
}
