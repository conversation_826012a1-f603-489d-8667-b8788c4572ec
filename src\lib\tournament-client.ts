// Client-side tournament service that makes API calls instead of direct database access

export interface CreateTournamentInput {
  name: string;
  format: "ROUND_ROBIN" | "SINGLE_ELIMINATION" | "DOUBLE_ELIMINATION";
  participantIds: string[];
  scheduledStart?: Date;
  matchInterval?: number;
  timeWindow?: string;
}

export interface TournamentWithDetails {
  id: string;
  name: string;
  format: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  scheduledStart?: string;
  startedAt?: string;
  completedAt?: string;
  matchInterval?: number;
  timeWindow?: string;
  participants: Array<{
    id: string;
    tournamentId: string;
    profileId: string;
    profile: {
      id: string;
      name: string;
      model: string;
      eloRating: number;
    };
  }>;
  matches: Array<{
    id: string;
    tournamentId: string;
    whiteProfileId: string;
    blackProfileId: string;
    status: string;
    result?: string;
    whiteProfile: {
      id: string;
      name: string;
      model: string;
    };
    blackProfile: {
      id: string;
      name: string;
      model: string;
    };
  }>;
}

export class TournamentClientService {
  async getAllTournaments(): Promise<TournamentWithDetails[]> {
    try {
      const response = await fetch("/api/tournaments");
      if (!response.ok) {
        throw new Error(`Failed to fetch tournaments: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error("Error fetching tournaments:", error);
      throw error;
    }
  }

  async getTournamentById(id: string): Promise<TournamentWithDetails | null> {
    try {
      const response = await fetch(`/api/tournaments/${id}`);
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to fetch tournament: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error("Error fetching tournament:", error);
      throw error;
    }
  }

  async createTournament(
    input: CreateTournamentInput,
  ): Promise<TournamentWithDetails> {
    try {
      const response = await fetch("/api/tournaments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(input),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Failed to create tournament: ${response.status}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error creating tournament:", error);
      throw error;
    }
  }

  async startTournament(id: string): Promise<TournamentWithDetails> {
    try {
      const response = await fetch(`/api/tournaments/${id}/start`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Failed to start tournament: ${response.status}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error starting tournament:", error);
      throw error;
    }
  }

  async pauseTournament(id: string): Promise<TournamentWithDetails> {
    try {
      const response = await fetch(`/api/tournaments/${id}/pause`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Failed to pause tournament: ${response.status}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error pausing tournament:", error);
      throw error;
    }
  }

  async resumeTournament(id: string): Promise<TournamentWithDetails> {
    try {
      const response = await fetch(`/api/tournaments/${id}/resume`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Failed to resume tournament: ${response.status}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error resuming tournament:", error);
      throw error;
    }
  }

  async getTournamentProgress(id: string): Promise<any> {
    try {
      const response = await fetch(`/api/tournaments/${id}/progress`);
      if (!response.ok) {
        throw new Error(
          `Failed to fetch tournament progress: ${response.status}`,
        );
      }
      return await response.json();
    } catch (error) {
      console.error("Error fetching tournament progress:", error);
      throw error;
    }
  }

  async retryFailedMatches(id: string): Promise<TournamentWithDetails> {
    try {
      const response = await fetch(`/api/tournaments/${id}/retry-failed`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error ||
            `Failed to retry failed matches: ${response.status}`,
        );
      }

      return await response.json();
    } catch (error) {
      console.error("Error retrying failed matches:", error);
      throw error;
    }
  }

  async deleteTournament(id: string): Promise<void> {
    try {
      const response = await fetch(`/api/tournaments/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.error || `Failed to delete tournament: ${response.status}`,
        );
      }
    } catch (error) {
      console.error("Error deleting tournament:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const tournamentService = new TournamentClientService();
