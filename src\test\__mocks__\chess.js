// Mock for chess.js library
export class Chess {
  constructor(fen) {
    this._fen =
      fen || "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1";
    this._pgn = "";
    this._history = [];
    this._turn = "w";
    this._isGameOver = false;
    this._headers = {};
  }

  clear() {
    this._fen = "8/8/8/8/8/8/8/8 w - - 0 1";
    this._pgn = "";
    this._history = [];
    this._turn = "w";
    this._isGameOver = false;
    this._headers = {};
    return this;
  }

  reset() {
    this._fen = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1";
    this._pgn = "";
    this._history = [];
    this._turn = "w";
    this._isGameOver = false;
    this._headers = {};
    return this;
  }

  load(fen) {
    this._fen = fen;
    return true;
  }

  loadPgn(pgn) {
    this._pgn = pgn;
    // Parse basic moves from PGN for testing
    const moves =
      pgn.match(/\b[a-h][1-8]|[RNBQK][a-h]?[1-8]?\w*|O-O(-O)?/g) || [];
    this._history = moves.map((move) => ({ san: move }));
    return true;
  }

  move(move) {
    if (typeof move === "string") {
      const moveObj = { san: move, from: "e2", to: "e4" };
      this._history.push(moveObj);
      this._turn = this._turn === "w" ? "b" : "w";
      return moveObj;
    } else if (move && move.from && move.to) {
      const moveObj = { ...move, san: move.san || `${move.from}-${move.to}` };
      this._history.push(moveObj);
      this._turn = this._turn === "w" ? "b" : "w";
      return moveObj;
    }
    return null;
  }

  fen() {
    return this._fen;
  }

  pgn() {
    return (
      this._pgn ||
      this._history
        .map((move, i) => {
          if (i % 2 === 0) {
            return `${Math.floor(i / 2) + 1}. ${move.san}`;
          } else {
            return move.san;
          }
        })
        .join(" ")
    );
  }

  turn() {
    return this._turn;
  }

  isGameOver() {
    return this._isGameOver;
  }

  isCheck() {
    return false;
  }

  isCheckmate() {
    return false;
  }

  isStalemate() {
    return false;
  }

  isDraw() {
    return false;
  }

  isThreefoldRepetition() {
    return false;
  }

  isInsufficientMaterial() {
    return false;
  }

  history(options = {}) {
    if (options.verbose) {
      return this._history.map((move, i) => ({
        ...move,
        color: i % 2 === 0 ? "w" : "b",
        piece: "p",
        from: move.from || "e2",
        to: move.to || "e4",
      }));
    }
    return this._history.map((move) => move.san);
  }

  moves(options = {}) {
    // Return available moves (mock)
    return ["e4", "e3", "d4", "d3", "Nf3", "Nc3"];
  }

  header(key, value) {
    if (arguments.length === 1) {
      return key ? this._headers[key] : this._headers;
    } else if (arguments.length === 2) {
      this._headers[key] = value;
      return this;
    }
    return this._headers;
  }

  setHeader(key, value) {
    this._headers[key] = value;
    return this;
  }

  getComment() {
    return "";
  }

  setComment(comment) {
    return this;
  }

  deleteComment() {
    return this;
  }

  getComments() {
    return [];
  }

  deleteComments() {
    return this;
  }
}
