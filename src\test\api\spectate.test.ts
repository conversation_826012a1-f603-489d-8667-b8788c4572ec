import { describe, it, expect, vi, beforeEach } from "vitest";
import { GET } from "@/app/api/spectate/[gameId]/route";
import { NextRequest } from "next/server";

// Mock Prisma with proper hoisting
vi.mock("@/lib/db", () => ({
  default: {
    game: {
      findUnique: vi.fn(),
    },
  },
  withDatabaseRetry: vi.fn((fn) => fn()),
}));

// Mock GameStatusService
vi.mock("@/lib/game-status-service", () => ({
  GameStatusService: {
    addSpectator: vi.fn(),
    removeSpectator: vi.fn(),
  },
}));

// Import the mock after defining it
import prisma from "@/lib/db";
const mockPrisma = prisma as any;

// Mock WritableStream
class MockWritableStreamDefaultWriter {
  closed = false;

  write = vi.fn().mockImplementation((data) => {
    if (this.closed) {
      throw new Error("Invalid state: WritableStream is closed");
    }
    return Promise.resolve();
  });

  close = vi.fn().mockImplementation(() => {
    this.closed = true;
    return Promise.resolve();
  });

  abort = vi.fn();
}

class MockWritableStream {
  writer = new MockWritableStreamDefaultWriter();

  getWriter() {
    return this.writer;
  }
}

// Mock ReadableStream
class MockReadableStream {
  constructor(private source: any) {}

  getReader() {
    return {
      read: vi.fn().mockResolvedValue({ done: true, value: undefined }),
      releaseLock: vi.fn(),
    };
  }
}

describe("/api/spectate/[gameId]", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Mock setInterval to not actually run intervals
    vi.spyOn(global, "setInterval").mockImplementation((fn, ms) => {
      return 1 as any; // Return fake timer ID
    });

    // Mock clearInterval
    vi.spyOn(global, "clearInterval").mockImplementation(() => {});

    // Mock TransformStream
    global.TransformStream = vi.fn().mockImplementation(() => ({
      readable: new MockReadableStream({}),
      writable: new MockWritableStream(),
    })) as any;

    // Mock Response constructor
    global.Response = vi.fn().mockImplementation((body, init) => {
      const stream = new MockWritableStream();
      return {
        body: new MockReadableStream({}),
        headers: new Map(),
        status: init?.status || 200,
        ok: true,
        ...init,
      };
    }) as any;
  });

  it("should handle valid game spectating", async () => {
    const mockGame = {
      id: "test-game-id",
      pgn: "1. e4 e5",
      status: "IN_PROGRESS",
    };

    mockPrisma.game.findUnique.mockResolvedValue(mockGame);

    const request = new NextRequest(
      "http://localhost:3000/api/spectate/test-game-id",
    );

    // Mock the params
    const mockParams = { gameId: "test-game-id" };

    const response = await GET(request, {
      params: Promise.resolve(mockParams),
    });

    expect(response).toBeDefined();
    expect(mockPrisma.game.findUnique).toHaveBeenCalledWith({
      where: { id: "test-game-id" },
    });
  });

  it("should handle non-existent game", async () => {
    mockPrisma.game.findUnique.mockResolvedValue(null);

    const request = new NextRequest(
      "http://localhost:3000/api/spectate/non-existent",
    );
    const mockParams = { gameId: "non-existent" };

    const response = await GET(request, {
      params: Promise.resolve(mockParams),
    });

    expect(response).toBeDefined();
    expect(mockPrisma.game.findUnique).toHaveBeenCalledWith({
      where: { id: "non-existent" },
    });
  });

  it("should handle database error", async () => {
    mockPrisma.game.findUnique.mockRejectedValue(new Error("Database error"));

    const request = new NextRequest(
      "http://localhost:3000/api/spectate/test-game-id",
    );
    const mockParams = { gameId: "test-game-id" };

    const response = await GET(request, {
      params: Promise.resolve(mockParams),
    });

    expect(response).toBeDefined();
  });

  it("should handle closed writer stream", async () => {
    const mockGame = {
      id: "test-game-id",
      pgn: "1. e4 e5",
      status: "IN_PROGRESS",
    };

    mockPrisma.game.findUnique.mockResolvedValue(mockGame);

    const request = new NextRequest(
      "http://localhost:3000/api/spectate/test-game-id",
    );
    const mockParams = { gameId: "test-game-id" };

    // Simulate closed writer
    const response = await GET(request, {
      params: Promise.resolve(mockParams),
    });

    expect(response).toBeDefined();
  });

  it("should validate gameId parameter", async () => {
    const request = new NextRequest("http://localhost:3000/api/spectate/");
    const mockParams = { gameId: "" };

    const response = await GET(request, {
      params: Promise.resolve(mockParams),
    });

    expect(response).toBeDefined();
  });

  it("should handle malformed gameId", async () => {
    const request = new NextRequest(
      "http://localhost:3000/api/spectate/invalid-id",
    );
    const mockParams = { gameId: "invalid-id" };

    mockPrisma.game.findUnique.mockResolvedValue(null);

    const response = await GET(request, {
      params: Promise.resolve(mockParams),
    });

    expect(response).toBeDefined();
    expect(mockPrisma.game.findUnique).toHaveBeenCalledWith({
      where: { id: "invalid-id" },
    });
  });
});
