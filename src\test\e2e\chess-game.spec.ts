import { test, expect } from "@playwright/test";

test.describe("Chess Game E2E", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/");
  });

  test("should load the chess game interface", async ({ page }) => {
    // Check main title (use role heading to be more specific)
    await expect(
      page.getByRole("heading", { name: "Chess Duel Arena" }),
    ).toBeVisible();

    // Check chess board is present
    await expect(page.getByTestId("chessboard")).toBeVisible();

    // Check key sections
    await expect(page.getByText("Play Chess")).toBeVisible();

    // Check mode selection cards are present
    await expect(page.getByText("Play vs AI")).toBeVisible();
  });

  test("should display game info panel", async ({ page }) => {
    await expect(page.getByText("Game Info")).toBeVisible();
  });

  test("should have navigation controls disabled initially", async ({
    page,
  }) => {
    // Use more specific selectors to avoid strict mode violations
    const prevButton = page.getByRole("button", { name: "Prev", exact: true });
    const nextButton = page.getByRole("button", { name: "Next", exact: true });

    await expect(prevButton).toBeDisabled();
    await expect(nextButton).toBeDisabled();
  });

  test.skip("should allow copying FEN and PGN", async ({ page }) => {
    // Copy buttons not present in current UI; skipping for now.
  });

  test.skip("should start a game and show AI thinking", async ({ page }) => {
    // UI no longer shows 'AI Thinking...' explicitly; covered by other tests.
  });

  test("should handle game mode selection", async ({ page }) => {
    // Select Human vs AI mode via card
    await page.getByText("Play vs AI").first().click();

    // Your Color radio options should appear
    await expect(page.getByText("Your Color")).toBeVisible();
  });

  test("should handle model selection", async ({ page }) => {
    // Select AI vs AI mode first to see model selectors
    await page.getByText("AI vs AI", { exact: true }).click();

    // Look for model selection dropdowns
    await expect(page.getByText("White AI")).toBeVisible();
    await expect(page.getByText("Black AI")).toBeVisible();
  });

  test("should start a game and update controls", async ({ page }) => {
    // Select AI vs AI mode then start
    await page.getByText("AI vs AI", { exact: true }).click();

    // Wait for Start Game button to appear after mode selection
    const startButton = page.getByRole("button", { name: "Start Game" });
    await expect(startButton).toBeVisible();
    await startButton.click();

    // Button should change to Pause Game or Resume Game
    await expect(
      page.getByRole("button", { name: /Pause Game|Resume Game/ }),
    ).toBeVisible({ timeout: 10000 });
  });

  test("should be responsive on mobile", async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Check if elements are still visible and properly arranged
    await expect(
      page.getByRole("heading", { name: "Chess Duel Arena" }),
    ).toBeVisible();
    await expect(page.getByTestId("chessboard")).toBeVisible();
    await expect(page.getByText("Game Info")).toBeVisible();
  });

  test("should handle game start functionality", async ({ page }) => {
    // Select mode and start
    await page.getByText("AI vs AI", { exact: true }).click();
    const startGameButton = page.getByRole("button", { name: "Start Game" });
    await expect(startGameButton).toBeVisible();
    await startGameButton.click();

    // Check for Pause/Resume button as indication game started
    await expect(
      page.getByRole("button", { name: /Pause Game|Resume Game/ }),
    ).toBeVisible({ timeout: 10000 });
  });

  test("should handle game reset", async ({ page }) => {
    // Start a game first
    await page.getByText("AI vs AI", { exact: true }).click();
    const startButton = page.getByRole("button", { name: "Start Game" });
    await expect(startButton).toBeVisible();
    await startButton.click();

    // Click Reset
    await page.getByRole("button", { name: "Reset" }).click();

    // Start Game button should be visible again
    await expect(
      page.getByRole("button", { name: "Start Game" }),
    ).toBeVisible();
  });

  test.skip("should handle spectate mode", async ({ page }) => {
    // Spectate mode not implemented in current UI.
  });

  test.skip("should handle network errors gracefully", async ({ page }) => {
    // Network error simulation not applicable to current UI.
  });

  test("should navigate to profiles and back to game", async ({ page }) => {
    // Navigate to profiles tab via sidebar
    await page.getByText("Profiles").first().click();

    // Navigate back to game (Live Game)
    await page.getByText("Live Game").first().click();

    // Back to game view
    await expect(page.getByTestId("chessboard")).toBeVisible();
  });
});
