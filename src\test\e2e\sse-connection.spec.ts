import { test, expect } from "@playwright/test";

test.describe("SSE Connection End-to-End Tests", () => {
  test.beforeEach(async ({ page }) => {
    // Enable console logging
    page.on("console", (msg) => {
      if (
        msg.type() === "error" ||
        msg.text().includes("SSE") ||
        msg.text().includes("SPECTATE")
      ) {
        console.log(`🖥️ BROWSER: ${msg.type()}: ${msg.text()}`);
      }
    });

    // Enable network logging
    page.on("response", (response) => {
      if (response.url().includes("/api/spectate/")) {
        console.log(`🌐 NETWORK: ${response.status()} ${response.url()}`);
      }
    });

    await page.goto("/");
  });

  test("should establish stable SSE connection without reconnecting loop", async ({
    page,
  }) => {
    console.log("🧪 TEST: Starting SSE connection stability test");

    // Start a game to create a spectatable game
    await page.getByText("Start Game").click();
    console.log("🧪 TEST: Game started");

    // Wait for autorun to create a game
    await page.waitForTimeout(3000);

    // Game was already started with Start Game button above

    // Monitor console logs for reconnection patterns
    let reconnectCount = 0;
    let connectionEstablished = false;

    page.on("console", (msg) => {
      const text = msg.text();
      if (text.includes("SPECTATE: Connecting to SSE stream")) {
        reconnectCount++;
        console.log(`🔄 TEST: Reconnection attempt #${reconnectCount}`);
      }
      if (text.includes("SPECTATE: SSE connection established")) {
        connectionEstablished = true;
        console.log("✅ TEST: Connection established");
      }
    });

    // Wait and monitor for 30 seconds
    await page.waitForTimeout(30000);

    // Check if connection was established
    expect(connectionEstablished).toBe(true);

    // Check that reconnections are not excessive (should be <= 3 in 30 seconds)
    expect(reconnectCount).toBeLessThanOrEqual(3);

    console.log(`🧪 TEST: Final reconnect count: ${reconnectCount}`);
  });

  test("should handle SSE messages correctly", async ({ page }) => {
    console.log("🧪 TEST: Starting SSE message handling test");

    let messagesReceived = 0;
    let heartbeatsReceived = 0;
    let gameUpdatesReceived = 0;

    page.on("console", (msg) => {
      const text = msg.text();
      if (text.includes("SPECTATE_UPDATE: Received")) {
        messagesReceived++;
        if (text.includes("heartbeat")) {
          heartbeatsReceived++;
        }
        if (text.includes("game_update") || text.includes("game_state")) {
          gameUpdatesReceived++;
        }
        console.log(`📨 TEST: Message received (${messagesReceived}): ${text}`);
      }
    });

    // Start game to create a live game
    await page.getByText("Start Game").first().click();
    console.log("🧪 TEST: Game started for message test");

    // Wait for messages to be received
    await page.waitForTimeout(45000); // Wait 45 seconds

    // Should receive at least some messages
    expect(messagesReceived).toBeGreaterThan(0);

    // Should receive heartbeats (at least 1 in 45 seconds with 30s interval)
    expect(heartbeatsReceived).toBeGreaterThan(0);

    console.log(
      `🧪 TEST: Messages: ${messagesReceived}, Heartbeats: ${heartbeatsReceived}, Game updates: ${gameUpdatesReceived}`,
    );
  });

  test("should recover from connection errors gracefully", async ({ page }) => {
    console.log("🧪 TEST: Starting connection recovery test");

    // Start game
    await page.getByText("Start Game").first().click();
    await page.waitForTimeout(5000);

    let errorCount = 0;
    let recoveryCount = 0;

    page.on("console", (msg) => {
      const text = msg.text();
      if (text.includes("SPECTATE_ERROR")) {
        errorCount++;
        console.log(`❌ TEST: Error detected (${errorCount}): ${text}`);
      }
      if (text.includes("Attempting reconnection")) {
        recoveryCount++;
        console.log(`🔄 TEST: Recovery attempt (${recoveryCount}): ${text}`);
      }
    });

    // Simulate network interruption by blocking the spectate endpoint temporarily
    await page.route("/api/spectate/*", (route) => {
      console.log("🚫 TEST: Blocking spectate request");
      route.abort();
    });

    // Wait for errors to occur
    await page.waitForTimeout(10000);

    // Unblock the endpoint
    await page.unroute("/api/spectate/*");
    console.log("✅ TEST: Unblocked spectate requests");

    // Wait for recovery
    await page.waitForTimeout(15000);

    // Should have detected errors and attempted recovery
    expect(errorCount).toBeGreaterThan(0);
    expect(recoveryCount).toBeGreaterThan(0);

    console.log(
      `🧪 TEST: Errors: ${errorCount}, Recovery attempts: ${recoveryCount}`,
    );
  });

  test("should not create multiple connections for same game", async ({
    page,
  }) => {
    console.log("🧪 TEST: Starting multiple connection prevention test");

    let connectionAttempts = 0;

    // Monitor network requests to spectate endpoint
    page.on("request", (request) => {
      if (request.url().includes("/api/spectate/")) {
        connectionAttempts++;
        console.log(
          `🌐 TEST: Connection attempt #${connectionAttempts} to ${request.url()}`,
        );
      }
    });

    // Start game
    await page.getByText("Start Game").first().click();
    await page.waitForTimeout(5000);

    // Try to trigger multiple connections by clicking spectate multiple times
    const spectateButtons = page.getByText("👁️");
    const count = await spectateButtons.count();

    if (count > 0) {
      // Click the first spectate button multiple times rapidly
      for (let i = 0; i < 3; i++) {
        await spectateButtons.first().click();
        await page.waitForTimeout(100);
      }
    }

    // Wait and monitor
    await page.waitForTimeout(10000);

    // Should not have excessive connection attempts (allow some reconnections)
    expect(connectionAttempts).toBeLessThanOrEqual(5);

    console.log(`🧪 TEST: Total connection attempts: ${connectionAttempts}`);
  });

  test("should display correct connection status in UI", async ({ page }) => {
    console.log("🧪 TEST: Starting UI status display test");

    // Start game
    await page.getByText("Start Game").first().click();
    await page.waitForTimeout(3000);

    // Check for reconnecting indicator
    const reconnectingBadge = page.getByText("Reconnecting...");

    // Should eventually show connected state (not reconnecting)
    await page.waitForTimeout(10000);

    // Reconnecting badge should not be permanently visible
    const isReconnectingVisible = await reconnectingBadge
      .isVisible()
      .catch(() => false);

    if (isReconnectingVisible) {
      // If still reconnecting after 10 seconds, wait a bit more
      await page.waitForTimeout(10000);
      const stillReconnecting = await reconnectingBadge
        .isVisible()
        .catch(() => false);
      expect(stillReconnecting).toBe(false);
    }

    console.log("🧪 TEST: UI status check completed");
  });
});
