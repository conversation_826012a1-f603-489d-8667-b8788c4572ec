import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useGameState } from "@/hooks/useGameState";

// Mock the fetch function
global.fetch = vi.fn();

describe("useGameState Hook", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
  });

  it("initializes with default state", () => {
    const { result } = renderHook(() => useGameState());

    expect(result.current.currentGameState).toBe("idle");
    expect(result.current.logs).toEqual(["Welcome to Chess Duel Arena!"]);
    expect(result.current.isGameRunning).toBe(false);
    expect(result.current.isGameStarted).toBe(false);
  });

  it("starts a game correctly", () => {
    const { result } = renderHook(() => useGameState());

    act(() => {
      result.current.startGame();
    });

    expect(result.current.currentGameState).toBe("starting");
    expect(result.current.isGameRunning).toBe(true);
    expect(result.current.isGameStarted).toBe(true);
  });

  it("pauses a running game", () => {
    const { result } = renderHook(() => useGameState());

    // First start the game
    act(() => {
      result.current.startGame();
    });

    // Then pause it
    act(() => {
      result.current.pauseGame();
    });

    expect(result.current.currentGameState).toBe("paused");
    expect(result.current.isGameRunning).toBe(false);
    expect(result.current.isGameStarted).toBe(true);
  });

  it("resumes a paused game", () => {
    const { result } = renderHook(() => useGameState());

    // Start and pause the game first
    act(() => {
      result.current.startGame();
      result.current.pauseGame();
    });

    // Resume the game
    act(() => {
      result.current.resumeGame();
    });

    expect(result.current.currentGameState).toBe("running");
    expect(result.current.isGameRunning).toBe(true);
  });

  it("ends a game correctly", () => {
    const { result } = renderHook(() => useGameState());

    // Start the game first
    act(() => {
      result.current.startGame();
    });

    // End the game
    act(() => {
      result.current.endGame("checkmate");
    });

    expect(result.current.currentGameState).toBe("completed");
    expect(result.current.isGameRunning).toBe(false);
  });

  it("resets game state", () => {
    const { result } = renderHook(() => useGameState());

    // Set some state
    act(() => {
      result.current.startGame();
      result.current.addLog("Test log");
    });

    // Reset
    act(() => {
      result.current.resetGameState();
    });

    expect(result.current.currentGameState).toBe("idle");
    expect(result.current.logs).toEqual(["Game reset. Select mode, models and start."]);
    expect(result.current.isGameRunning).toBe(false);
    expect(result.current.isGameStarted).toBe(false);
  });

  it("adds logs correctly", () => {
    const { result } = renderHook(() => useGameState());

    act(() => {
      result.current.addLog("First log");
      result.current.addLog("Second log");
    });

    expect(result.current.logs).toHaveLength(3); // Including initial welcome message
    expect(result.current.logs[0]).toContain("Second log"); // Latest log first
    expect(result.current.logs[1]).toContain("First log");
    expect(result.current.logs[2]).toContain("Welcome to Chess Duel Arena!"); // Initial message last
  });

  it("handles game mode changes", () => {
    const { result } = renderHook(() => useGameState());

    act(() => {
      result.current.setGameMode("ai-vs-ai");
    });

    expect(result.current.gameMode).toBe("ai-vs-ai");
  });

  it("handles model selection", () => {
    const { result } = renderHook(() => useGameState());

    act(() => {
      result.current.setWhiteModel("gemini-2.0-flash");
      result.current.setBlackModel("gemini-1.5-pro");
    });

    expect(result.current.whiteModel).toBe("gemini-2.0-flash");
    expect(result.current.blackModel).toBe("gemini-1.5-pro");
  });

  it("handles human player selection", () => {
    const { result } = renderHook(() => useGameState());

    act(() => {
      result.current.setHumanPlayer("white");
    });

    expect(result.current.humanPlayer).toBe("white");

    act(() => {
      result.current.setHumanPlayer("black");
    });

    expect(result.current.humanPlayer).toBe("black");
  });
});
