import { vi } from "vitest";

// Mock environment variables for integration tests
process.env.NODE_ENV = "test";
process.env.DATABASE_URL = "file:./test.db";
process.env.NEXTAUTH_SECRET = "test-secret";
process.env.NEXTAUTH_URL = "http://localhost:3000";

// Mock Prisma for integration tests
vi.mock("@/lib/db", () => ({
  prisma: {
    $connect: vi.fn(),
    $disconnect: vi.fn(),
    profile: {
      create: vi.fn(),
      findMany: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    game: {
      create: vi.fn(),
      findMany: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    tournament: {
      create: vi.fn(),
      findMany: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
    match: {
      create: vi.fn(),
      findMany: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
    },
  },
}));

// Mock Genkit AI for integration tests
vi.mock("@/ai/genkit", () => ({
  generateMove: vi.fn().mockResolvedValue({
    move: "e4",
    reason: "Opening move",
    opponentPrediction: "e5",
    analysis: [],
  }),
}));

// Mock fetch for API calls
global.fetch = vi.fn();

// Mock console methods to reduce noise in integration tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};
