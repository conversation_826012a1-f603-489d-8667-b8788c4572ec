import { describe, it, expect, vi, beforeEach } from "vitest";
import { profileService } from "@/lib/profile-client";
import { mockProfile, mockFetch } from "../utils";

// Mock fetch globally
global.fetch = vi.fn();

describe("ProfileClientService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getAllProfiles", () => {
    it("should fetch all profiles successfully", async () => {
      const mockProfiles = [mockProfile];
      global.fetch = mockFetch(mockProfiles);

      const result = await profileService.getAllProfiles();

      expect(fetch).toHaveBeenCalledWith("/api/profiles");
      expect(result).toEqual(mockProfiles);
    });

    it("should handle fetch error", async () => {
      global.fetch = mockFetch(null, false, 500);

      await expect(profileService.getAllProfiles()).rejects.toThrow(
        "Failed to fetch profiles: 500",
      );
    });

    it("should handle network error", async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error("Network error"));

      await expect(profileService.getAllProfiles()).rejects.toThrow(
        "Network error",
      );
    });
  });

  describe("getProfileById", () => {
    it("should fetch profile by id successfully", async () => {
      global.fetch = mockFetch(mockProfile);

      const result = await profileService.getProfileById("test-id");

      expect(fetch).toHaveBeenCalledWith("/api/profiles/test-id");
      expect(result).toEqual(mockProfile);
    });

    it("should return null for 404", async () => {
      global.fetch = mockFetch(null, false, 404);

      const result = await profileService.getProfileById("non-existent");

      expect(result).toBeNull();
    });

    it("should throw error for other status codes", async () => {
      global.fetch = mockFetch(null, false, 500);

      await expect(profileService.getProfileById("test-id")).rejects.toThrow(
        "Failed to fetch profile: 500",
      );
    });
  });

  describe("createProfile", () => {
    it("should create profile successfully", async () => {
      const newProfile = { name: "New Profile", model: "gpt-4" };
      global.fetch = mockFetch(mockProfile);

      const result = await profileService.createProfile(newProfile);

      expect(fetch).toHaveBeenCalledWith("/api/profiles", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newProfile),
      });
      expect(result).toEqual(mockProfile);
    });

    it("should handle creation error", async () => {
      const newProfile = { name: "New Profile", model: "gpt-4" };
      global.fetch = mockFetch({ error: "Validation failed" }, false, 400);

      await expect(profileService.createProfile(newProfile)).rejects.toThrow(
        "Validation failed",
      );
    });
  });

  describe("updateProfile", () => {
    it("should update profile successfully", async () => {
      const updateData = { name: "Updated Profile" };
      global.fetch = mockFetch(mockProfile);

      const result = await profileService.updateProfile("test-id", updateData);

      expect(fetch).toHaveBeenCalledWith("/api/profiles/test-id", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(updateData),
      });
      expect(result).toEqual(mockProfile);
    });
  });

  describe("deleteProfile", () => {
    it("should delete profile successfully", async () => {
      global.fetch = mockFetch({});

      await profileService.deleteProfile("test-id");

      expect(fetch).toHaveBeenCalledWith("/api/profiles/test-id", {
        method: "DELETE",
      });
    });

    it("should handle deletion error", async () => {
      global.fetch = mockFetch({ error: "Cannot delete" }, false, 400);

      await expect(profileService.deleteProfile("test-id")).rejects.toThrow(
        "Cannot delete",
      );
    });
  });

  describe("getProfileStats", () => {
    it("should fetch profile stats successfully", async () => {
      const mockStats = { winRate: 0.6, recentMatches: [] };
      global.fetch = mockFetch(mockStats);

      const result = await profileService.getProfileStats("test-id");

      expect(fetch).toHaveBeenCalledWith("/api/profiles/test-id/stats");
      expect(result).toEqual(mockStats);
    });
  });
});
