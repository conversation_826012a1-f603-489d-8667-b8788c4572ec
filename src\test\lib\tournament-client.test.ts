import { describe, it, expect, vi, beforeEach } from "vitest";
import { tournamentService } from "@/lib/tournament-client";
import { mockTournament, mockFetch } from "../utils";

global.fetch = vi.fn();

describe("TournamentClientService", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("getAllTournaments", () => {
    it("should fetch all tournaments successfully", async () => {
      const mockTournaments = [mockTournament];
      global.fetch = mockFetch(mockTournaments);

      const result = await tournamentService.getAllTournaments();

      expect(fetch).toHaveBeenCalledWith("/api/tournaments");
      expect(result).toEqual(mockTournaments);
    });

    it("should handle fetch error", async () => {
      global.fetch = mockFetch(null, false, 500);

      await expect(tournamentService.getAllTournaments()).rejects.toThrow(
        "Failed to fetch tournaments: 500",
      );
    });
  });

  describe("getTournamentById", () => {
    it("should fetch tournament by id successfully", async () => {
      global.fetch = mockFetch(mockTournament);

      const result = await tournamentService.getTournamentById("test-id");

      expect(fetch).toHaveBeenCalledWith("/api/tournaments/test-id");
      expect(result).toEqual(mockTournament);
    });

    it("should return null for 404", async () => {
      global.fetch = mockFetch(null, false, 404);

      const result = await tournamentService.getTournamentById("non-existent");

      expect(result).toBeNull();
    });
  });

  describe("createTournament", () => {
    it("should create tournament successfully", async () => {
      const newTournament = {
        name: "Test Tournament",
        format: "ROUND_ROBIN" as const,
        participantIds: ["p1", "p2"],
      };
      global.fetch = mockFetch(mockTournament);

      const result = await tournamentService.createTournament(newTournament);

      expect(fetch).toHaveBeenCalledWith("/api/tournaments", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newTournament),
      });
      expect(result).toEqual(mockTournament);
    });

    it("should handle creation error", async () => {
      const newTournament = {
        name: "Test Tournament",
        format: "ROUND_ROBIN" as const,
        participantIds: ["p1", "p2"],
      };
      global.fetch = mockFetch({ error: "Invalid participants" }, false, 400);

      await expect(
        tournamentService.createTournament(newTournament),
      ).rejects.toThrow("Invalid participants");
    });
  });

  describe("startTournament", () => {
    it("should start tournament successfully", async () => {
      global.fetch = mockFetch(mockTournament);

      const result = await tournamentService.startTournament("test-id");

      expect(fetch).toHaveBeenCalledWith("/api/tournaments/test-id/start", {
        method: "POST",
      });
      expect(result).toEqual(mockTournament);
    });

    it("should handle start error", async () => {
      global.fetch = mockFetch({ error: "Cannot start" }, false, 400);

      await expect(
        tournamentService.startTournament("test-id"),
      ).rejects.toThrow("Cannot start");
    });
  });

  describe("pauseTournament", () => {
    it("should pause tournament successfully", async () => {
      global.fetch = mockFetch(mockTournament);

      const result = await tournamentService.pauseTournament("test-id");

      expect(fetch).toHaveBeenCalledWith("/api/tournaments/test-id/pause", {
        method: "POST",
      });
      expect(result).toEqual(mockTournament);
    });
  });

  describe("resumeTournament", () => {
    it("should resume tournament successfully", async () => {
      global.fetch = mockFetch(mockTournament);

      const result = await tournamentService.resumeTournament("test-id");

      expect(fetch).toHaveBeenCalledWith("/api/tournaments/test-id/resume", {
        method: "POST",
      });
      expect(result).toEqual(mockTournament);
    });
  });

  describe("getTournamentProgress", () => {
    it("should fetch tournament progress successfully", async () => {
      const mockProgress = { completed: 5, total: 10, percentage: 50 };
      global.fetch = mockFetch(mockProgress);

      const result = await tournamentService.getTournamentProgress("test-id");

      expect(fetch).toHaveBeenCalledWith("/api/tournaments/test-id/progress");
      expect(result).toEqual(mockProgress);
    });
  });

  describe("retryFailedMatches", () => {
    it("should retry failed matches successfully", async () => {
      global.fetch = mockFetch(mockTournament);

      const result = await tournamentService.retryFailedMatches("test-id");

      expect(fetch).toHaveBeenCalledWith(
        "/api/tournaments/test-id/retry-failed",
        {
          method: "POST",
        },
      );
      expect(result).toEqual(mockTournament);
    });
  });

  describe("deleteTournament", () => {
    it("should delete tournament successfully", async () => {
      global.fetch = mockFetch({});

      await tournamentService.deleteTournament("test-id");

      expect(fetch).toHaveBeenCalledWith("/api/tournaments/test-id", {
        method: "DELETE",
      });
    });

    it("should handle deletion error", async () => {
      global.fetch = mockFetch(
        { error: "Cannot delete active tournament" },
        false,
        400,
      );

      await expect(
        tournamentService.deleteTournament("test-id"),
      ).rejects.toThrow("Cannot delete active tournament");
    });
  });
});
