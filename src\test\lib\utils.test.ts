import { describe, it, expect } from "vitest";
import { cn } from "@/lib/utils";

describe("Utils", () => {
  describe("cn (className utility)", () => {
    it("merges class names correctly", () => {
      const result = cn("base-class", "additional-class");
      expect(result).toContain("base-class");
      expect(result).toContain("additional-class");
    });

    it("handles conditional classes", () => {
      const isActive = true;
      const isDisabled = false;
      
      const result = cn(
        "base-class",
        isActive && "active-class",
        isDisabled && "disabled-class"
      );
      
      expect(result).toContain("base-class");
      expect(result).toContain("active-class");
      expect(result).not.toContain("disabled-class");
    });

    it("handles undefined and null values", () => {
      const result = cn("base-class", undefined, null, "valid-class");
      expect(result).toContain("base-class");
      expect(result).toContain("valid-class");
    });

    it("handles empty strings", () => {
      const result = cn("base-class", "", "valid-class");
      expect(result).toContain("base-class");
      expect(result).toContain("valid-class");
    });

    it("handles arrays of classes", () => {
      const result = cn(["class1", "class2"], "class3");
      expect(result).toContain("class1");
      expect(result).toContain("class2");
      expect(result).toContain("class3");
    });

    it("handles objects with boolean values", () => {
      const result = cn({
        "always-present": true,
        "never-present": false,
        "conditionally-present": true,
      });
      
      expect(result).toContain("always-present");
      expect(result).not.toContain("never-present");
      expect(result).toContain("conditionally-present");
    });

    it("merges conflicting Tailwind classes correctly", () => {
      // This tests the tailwind-merge functionality
      const result = cn("px-2 py-1", "px-4");
      // Should keep px-4 and py-1, removing px-2
      expect(result).toContain("px-4");
      expect(result).toContain("py-1");
      expect(result).not.toContain("px-2");
    });

    it("handles complex combinations", () => {
      const isLarge = true;
      const variant = "primary";
      
      const result = cn(
        "base-button",
        {
          "button-large": isLarge,
          "button-small": !isLarge,
        },
        variant === "primary" && "button-primary",
        variant === "secondary" && "button-secondary"
      );
      
      expect(result).toContain("base-button");
      expect(result).toContain("button-large");
      expect(result).toContain("button-primary");
      expect(result).not.toContain("button-small");
      expect(result).not.toContain("button-secondary");
    });

    it("returns empty string for no arguments", () => {
      const result = cn();
      expect(result).toBe("");
    });

    it("returns empty string for all falsy arguments", () => {
      const result = cn(false, null, undefined, "");
      expect(result).toBe("");
    });
  });
});
