import "@testing-library/jest-dom";
import { vi } from "vitest";

// Mock Next.js router
vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => "/",
}));

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock fetch
global.fetch = vi.fn();

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock chess.js globally
vi.mock("chess.js", () => {
  class Chess {
    private _fen: string;
    private _pgn: string;
    private _history: any[];
    private _turn: string;
    private _isGameOver: boolean;
    private _headers: Record<string, any>;

    constructor(fen?: any) {
      this._fen =
        fen || "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1";
      this._pgn = "";
      this._history = [];
      this._turn = "w";
      this._isGameOver = false;
      this._headers = {};
    }

    clear() {
      this._fen = "8/8/8/8/8/8/8/8 w - - 0 1";
      this._pgn = "";
      this._history = [];
      this._turn = "w";
      this._isGameOver = false;
      this._headers = {};
      return this;
    }

    reset() {
      this._fen = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1";
      this._pgn = "";
      this._history = [];
      this._turn = "w";
      this._isGameOver = false;
      this._headers = {};
      return this;
    }

    fen() {
      return this._fen;
    }
    pgn() {
      return this._pgn;
    }
    turn() {
      return this._turn;
    }
    isGameOver() {
      return this._isGameOver;
    }
    isCheck() {
      return false;
    }
    isCheckmate() {
      return false;
    }
    isStalemate() {
      return false;
    }
    isDraw() {
      return false;
    }
    move() {
      return { san: "e4" };
    }
    history() {
      return this._history;
    }
    moves() {
      return ["e4", "e3"];
    }
    load() {
      return true;
    }
    loadPgn() {
      return true;
    }
    header() {
      return this._headers;
    }
    setHeader() {
      return this;
    }
  }

  return { Chess };
});
