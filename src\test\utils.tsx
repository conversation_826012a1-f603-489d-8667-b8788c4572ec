import React from "react";
import { render, RenderOptions } from "@testing-library/react";
import { vi, beforeEach } from "vitest";

// Mock data
export const mockProfile = {
  id: "test-profile-1",
  name: "Test GPT",
  model: "gpt-4",
  eloRating: 1500,
  gamesPlayed: 10,
  wins: 5,
  losses: 3,
  draws: 2,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
};

export const mockTournament = {
  id: "test-tournament-1",
  name: "Test Tournament",
  format: "ROUND_ROBIN",
  status: "PENDING",
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  participants: [],
  matches: [],
};

export const mockGameActivity = {
  id: "test-activity-1",
  type: "game_start",
  whitePlayer: "GPT-4",
  blackPlayer: "Claude",
  whiteElo: 1500,
  blackElo: 1450,
  timestamp: new Date(),
};

// Mock API responses
export const mockFetch = (data: any, ok = true, status = 200) => {
  return vi.fn().mockResolvedValue({
    ok,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  });
};

// Mock chess.js
export const mockChessGame = {
  fen: () => "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
  pgn: () => "",
  history: () => [],
  turn: () => "w",
  isGameOver: () => false,
  isCheck: () => false,
  isCheckmate: () => false,
  isStalemate: () => false,
  isDraw: () => false,
  move: vi.fn().mockReturnValue({ san: "e4", from: "e2", to: "e4" }),
  undo: vi.fn(),
  reset: vi.fn(),
  load: vi.fn(),
  moves: vi.fn().mockReturnValue(["e4", "e3", "d4", "d3"]),
  header: vi.fn().mockReturnValue({ White: "White", Black: "Black" }),
};

// Mock Chessboard component
vi.mock("react-chessboard", () => ({
  Chessboard: ({ position, onPieceDrop, onSquareClick }: any) => (
    <div
      data-testid="chessboard"
      data-position={position}
      onClick={() => onSquareClick && onSquareClick("e2")}
    >
      Mocked Chessboard
    </div>
  ),
}));

// Mock chess.js
vi.mock("chess.js", () => ({
  Chess: vi.fn().mockImplementation(() => mockChessGame),
}));

// Custom render function
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, "wrapper">,
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from "@testing-library/react";
export { customRender as render };

// Test helpers
export const waitForLoadingToFinish = () =>
  new Promise((resolve) => setTimeout(resolve, 0));

export const createMockEvent = (type: string, data: any = {}) => ({
  type,
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: { value: "" },
  ...data,
});

// Mock toast
export const mockToast = vi.fn();
vi.mock("@/hooks/use-toast", () => ({
  useToast: () => ({
    toast: mockToast,
  }),
}));

// Mock window.addGameActivity
beforeEach(() => {
  (window as any).addGameActivity = vi.fn();
});
