import { vi } from "vitest";
import type { Profile, Game, Tournament, Match } from "@prisma/client";

// Mock data factories
export const createMockProfile = (overrides: Partial<Profile> = {}): Profile => ({
  id: "test-profile-id",
  name: "Test Player",
  model: "gemini-2.0-flash",
  elo: 1200,
  gamesPlayed: 0,
  wins: 0,
  losses: 0,
  draws: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

export const createMockGame = (overrides: Partial<Game> = {}): Game => ({
  id: "test-game-id",
  whitePlayerId: "white-player-id",
  blackPlayerId: "black-player-id",
  fen: "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
  pgn: "",
  status: "active",
  result: null,
  currentTurn: "white",
  moveCount: 0,
  timeControl: null,
  startedAt: new Date(),
  endedAt: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  tournamentId: null,
  ...overrides,
});

export const createMockTournament = (overrides: Partial<Tournament> = {}): Tournament => ({
  id: "test-tournament-id",
  name: "Test Tournament",
  description: "A test tournament",
  status: "pending",
  type: "round_robin",
  maxParticipants: 8,
  currentRound: 0,
  totalRounds: 3,
  settings: {},
  createdAt: new Date(),
  updatedAt: new Date(),
  startedAt: null,
  endedAt: null,
  ...overrides,
});

export const createMockMatch = (overrides: Partial<Match> = {}): Match => ({
  id: "test-match-id",
  tournamentId: "test-tournament-id",
  round: 1,
  whitePlayerId: "white-player-id",
  blackPlayerId: "black-player-id",
  gameId: null,
  status: "pending",
  result: null,
  scheduledAt: new Date(),
  startedAt: null,
  endedAt: null,
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// Test utilities
export const mockFetch = (response: any, status = 200) => {
  return vi.fn().mockResolvedValue({
    ok: status >= 200 && status < 300,
    status,
    json: vi.fn().mockResolvedValue(response),
    text: vi.fn().mockResolvedValue(JSON.stringify(response)),
  });
};

export const mockApiResponse = (data: any, status = 200) => ({
  status,
  data,
  error: status >= 400 ? "Error" : null,
});

// Chess game utilities
export const createMockChessGame = () => ({
  fen: () => "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
  pgn: () => "",
  turn: () => "w",
  isGameOver: () => false,
  isCheck: () => false,
  isCheckmate: () => false,
  isStalemate: () => false,
  isDraw: () => false,
  move: vi.fn().mockReturnValue({ san: "e4" }),
  history: () => [],
  moves: () => ["e4", "e3"],
  load: vi.fn().mockReturnValue(true),
  loadPgn: vi.fn().mockReturnValue(true),
  reset: vi.fn(),
  clear: vi.fn(),
});

// Async test helpers
export const waitFor = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export const flushPromises = () => new Promise(resolve => setImmediate(resolve));

// Error simulation helpers
export const simulateNetworkError = () => {
  throw new Error("Network error");
};

export const simulateDatabaseError = () => {
  throw new Error("Database connection failed");
};

// Mock timers helpers
export const advanceTimers = (ms: number) => {
  vi.advanceTimersByTime(ms);
};

export const runAllTimers = () => {
  vi.runAllTimers();
};
