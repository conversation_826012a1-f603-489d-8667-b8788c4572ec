// Simple test for ELO calculator
const { ELOCalculator } = require("./src/lib/elo-calculator.ts");

console.log("Testing ELO Calculator...");

try {
  const calculator = new ELOCalculator();

  // Test basic calculation
  const result = calculator.calculateEloChange(1900, 1900, 0, 0, "white");
  console.log("ELO calculation result:", result);

  // Test expected score calculation
  const expected = calculator.calculateExpectedScore(1900, 1700);
  console.log("Expected score (1900 vs 1700):", expected);

  console.log("ELO Calculator tests passed!");
} catch (error) {
  console.error("ELO Calculator test failed:", error.message);
}
