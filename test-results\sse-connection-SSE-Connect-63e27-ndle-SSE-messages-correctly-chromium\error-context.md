# Page snapshot

```yaml
- img
- text: Chess Arena AI Tournament System
- button "Toggle theme":
  - img
- list:
  - listitem:
    - button "Live Game":
      - img
      - text: Live Game
  - listitem:
    - button "Profiles":
      - img
      - text: Profiles
  - listitem:
    - button "Tournaments":
      - img
      - text: Tournaments
  - listitem:
    - button "History":
      - img
      - text: History
- text: AI Chess Tournament v1.2.0
- button "Toggle Sidebar"
- main:
  - button "Toggle Sidebar":
    - img
    - text: Toggle Sidebar
  - heading "Live Game" [level=1]:
    - img
    - text: Live Game
  - button "Toggle theme":
    - img
  - heading "Chess Duel Arena" [level=1]
  - paragraph: AI vs AI Chess Simulation
  - text: Play Chess Select a game mode
  - img
  - text: Ready
  - img
  - paragraph: Play vs AI
  - img
  - paragraph: AI vs AI
  - img
  - paragraph: Freestyle AI vs AI
  - img:
    - img
  - text: "8"
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - text: "7"
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - text: 6 5 4 3
  - img:
    - img
  - text: "2"
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - img:
    - img
  - text: 1 a
  - img:
    - img
  - text: b
  - img:
    - img
  - text: c
  - img:
    - img
  - text: d
  - img:
    - img
  - text: e
  - img:
    - img
  - text: f
  - img:
    - img
  - text: g
  - img:
    - img
  - text: h
  - img
  - text: Game has not started.
  - button "Prev" [disabled]:
    - img
    - text: Prev
  - text: "Move: 0 / 0"
  - button "Next" [disabled]:
    - img
    - text: Next
  - text: Game Info Welcome to Chess Duel Arena!
  - img
  - text: Game History 6 Previously completed games
  - button "Refresh":
    - img
    - text: Refresh
  - img
  - text: Draw gemini-2.0-flash vs gemini-2.5-flash
  - img
  - text: 5m 25s
  - img
  - text: 658 moves
  - img
  - text: 8/8/2025 06:56 AM
  - button "View":
    - img
    - text: View
  - button:
    - img
  - img
  - text: Black wins openrouter/moonshotai/kimi-k2:free vs gemini-2.5-flash
  - img
  - text: 1m 25s
  - img
  - text: 12 moves
  - img
  - text: 8/8/2025 03:09 AM
  - button "View":
    - img
    - text: View
  - button:
    - img
  - img
  - text: Black wins gemini-2.0-flash vs gemini-2.5-pro
  - img
  - text: 3m 42s
  - img
  - text: 12 moves
  - img
  - text: 8/8/2025 02:20 AM
  - button "View":
    - img
    - text: View
  - button:
    - img
  - img
  - text: Black wins gemini-2.0-flash vs openrouter/openai/gpt-oss-20b:free
  - img
  - text: 3m 51s
  - img
  - text: 107 moves
  - img
  - text: 8/8/2025 02:19 AM
  - button "View":
    - img
    - text: View
  - button:
    - img
  - img
  - text: Black wins gemini-2.0-flash vs gemini-2.5-flash
  - img
  - text: 12m 19s
  - img
  - text: 53 moves
  - img
  - text: 8/7/2025 11:58 PM
  - button "View":
    - img
    - text: View
  - button:
    - img
  - img
  - text: Black wins gemini-2.5-flash vs gemini-2.5-flash
  - img
  - text: 12m 5s
  - img
  - text: 237 moves
  - img
  - text: 8/7/2025 11:25 PM
  - button "View":
    - img
    - text: View
  - button:
    - img
- region "Notifications (F8)":
  - list
- alert
- button "Open Next.js Dev Tools":
  - img
```