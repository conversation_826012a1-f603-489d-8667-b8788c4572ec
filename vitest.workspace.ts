import { defineWorkspace } from "vitest/config";

export default defineWorkspace([
  // Unit tests configuration
  {
    extends: "./vitest.config.ts",
    test: {
      name: "unit",
      include: ["src/**/*.{test,spec}.{ts,tsx}"],
      exclude: [
        "**/node_modules/**",
        "**/e2e/**",
        "**/*.spec.ts", // E2E tests
        "**/dist/**",
        "**/.next/**",
      ],
      environment: "jsdom",
      setupFiles: ["./src/test/setup.ts"],
      globals: true,
      css: true,
      coverage: {
        provider: "v8",
        reporter: ["text", "json", "html", "lcov"],
        exclude: [
          "node_modules/",
          "src/test/",
          "**/*.d.ts",
          "**/*.config.*",
          "coverage/",
          ".next/",
          "prisma/",
          "**/__mocks__/**",
          "**/e2e/**",
        ],
        thresholds: {
          global: {
            branches: 70,
            functions: 70,
            lines: 70,
            statements: 70,
          },
        },
      },
    },
  },
  // Integration tests configuration
  {
    extends: "./vitest.config.ts",
    test: {
      name: "integration",
      include: ["src/**/*.integration.{test,spec}.{ts,tsx}"],
      environment: "node",
      setupFiles: ["./src/test/integration-setup.ts"],
      testTimeout: 30000,
      hookTimeout: 30000,
    },
  },
]);
